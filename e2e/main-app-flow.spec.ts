import { test, expect } from '@playwright/test';

test.describe('Main Application Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display homepage correctly', async ({ page }) => {
    // Check main heading
    await expect(page.getByRole('heading', { name: /Voice Emotion Analysis/i })).toBeVisible();
    
    // Check that key sections are visible
    await expect(page.getByText('Have an Access Code?')).toBeVisible();
    await expect(page.getByText('Get 10 Free Tries')).toBeVisible();
    
    // Check navigation
    await expect(page.getByRole('link', { name: 'Voice Emotion Analysis' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Home' })).toBeVisible();
  });

  test('should show sign in and sign up options for anonymous users', async ({ page }) => {
    // Check sign in button
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible();
    
    // Check sign up button
    await expect(page.getByRole('button', { name: 'Get 10 Free Tries' })).toBeVisible();
  });

  test('should open auth modal when sign in is clicked', async ({ page }) => {
    // Click sign in button
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Auth modal should be visible
    await expect(page.getByRole('dialog')).toBeVisible();
    
    // Should show sign in form
    await expect(page.getByPlaceholder('Email')).toBeVisible();
    await expect(page.getByPlaceholder('Password')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible();
  });

  test('should open auth modal when sign up is clicked', async ({ page }) => {
    // Click sign up button
    await page.getByRole('button', { name: 'Get 10 Free Tries' }).click();
    
    // Auth modal should be visible
    await expect(page.getByRole('dialog')).toBeVisible();
    
    // Should show sign up form
    await expect(page.getByPlaceholder('Email')).toBeVisible();
    await expect(page.getByPlaceholder('Password')).toBeVisible();
    await expect(page.getByRole('button', { name: 'Sign Up' })).toBeVisible();
  });

  test('should close auth modal when close button is clicked', async ({ page }) => {
    // Open auth modal
    await page.getByRole('button', { name: 'Sign In' }).click();
    await expect(page.getByRole('dialog')).toBeVisible();
    
    // Close modal
    await page.getByRole('button', { name: 'Close' }).click();
    
    // Modal should be hidden
    await expect(page.getByRole('dialog')).not.toBeVisible();
  });

  test('should close auth modal when clicking outside', async ({ page }) => {
    // Open auth modal
    await page.getByRole('button', { name: 'Sign In' }).click();
    await expect(page.getByRole('dialog')).toBeVisible();
    
    // Click outside the modal (on the backdrop)
    await page.locator('.fixed.inset-0').click({ position: { x: 10, y: 10 } });
    
    // Modal should be hidden
    await expect(page.getByRole('dialog')).not.toBeVisible();
  });

  test('should handle mobile menu toggle', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Mobile menu button should be visible
    const menuButton = page.getByRole('button', { name: 'Open menu' });
    await expect(menuButton).toBeVisible();
    
    // Desktop navigation should be hidden
    await expect(page.getByRole('link', { name: 'Home' })).not.toBeVisible();
    
    // Click menu button
    await menuButton.click();
    
    // Mobile menu should be visible
    await expect(page.getByRole('link', { name: 'Home' })).toBeVisible();
  });

  test('should have proper page title and meta information', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Voice Emotion Analysis/);
    
    // Check that the page has proper meta tags (this is basic SEO)
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveCount(1);
  });

  test('should handle keyboard navigation in header', async ({ page }) => {
    // Tab through header elements
    await page.keyboard.press('Tab');
    
    // Should focus on the logo/home link
    await expect(page.getByRole('link', { name: 'Voice Emotion Analysis' })).toBeFocused();
    
    // Continue tabbing
    await page.keyboard.press('Tab');
    await expect(page.getByRole('link', { name: 'Home' })).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByRole('button', { name: 'Sign In' })).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.getByRole('button', { name: 'Get 10 Free Tries' })).toBeFocused();
  });

  test('should show loading state initially', async ({ page }) => {
    // Navigate to page and check for loading indicators
    await page.goto('/');
    
    // The page should load without showing error states
    await expect(page.getByText('Error')).not.toBeVisible();
    await expect(page.getByText('Something went wrong')).not.toBeVisible();
  });

  test('should handle page refresh correctly', async ({ page }) => {
    // Navigate to page
    await page.goto('/');
    
    // Refresh the page
    await page.reload();
    
    // Should still show the main content
    await expect(page.getByRole('heading', { name: /Voice Emotion Analysis/i })).toBeVisible();
    await expect(page.getByText('Have an Access Code?')).toBeVisible();
  });

  test('should have responsive design elements', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.getByRole('link', { name: 'Home' })).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.getByRole('link', { name: 'Home' })).toBeVisible();
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.getByRole('button', { name: 'Open menu' })).toBeVisible();
  });

  test('should handle navigation between pages', async ({ page }) => {
    // Click on home link
    await page.getByRole('link', { name: 'Home' }).click();
    
    // Should stay on home page
    await expect(page).toHaveURL('/');
    
    // Click on logo
    await page.getByRole('link', { name: 'Voice Emotion Analysis' }).click();
    
    // Should go to home page
    await expect(page).toHaveURL('/');
  });

  test('should show proper error handling for network issues', async ({ page }) => {
    // Simulate offline condition
    await page.context().setOffline(true);
    
    // Try to interact with the app
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Should handle gracefully (not crash)
    // The exact behavior depends on implementation, but it shouldn't show unhandled errors
    await expect(page.getByText('Unhandled error')).not.toBeVisible();
    
    // Restore online condition
    await page.context().setOffline(false);
  });
});
