import { test, expect } from '@playwright/test';

test.describe('Access Code Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
  });

  test('should display access code input on homepage', async ({ page }) => {
    // Check that the access code input is visible
    await expect(page.getByPlaceholder('Enter your access code')).toBeVisible();
    
    // Check that the "Let's go!" button is initially disabled
    const button = page.getByRole('button', { name: "Let's go!" });
    await expect(button).toBeDisabled();
  });

  test('should enable button when access code is entered', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    const button = page.getByRole('button', { name: "Let's go!" });
    
    // Enter an access code
    await accessCodeInput.fill('testcode123');
    
    // Button should now be enabled
    await expect(button).toBeEnabled();
  });

  test('should handle invalid access code gracefully', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    const button = page.getByRole('button', { name: "Let's go!" });
    
    // Enter an invalid access code
    await accessCodeInput.fill('invalidcode');
    await button.click();
    
    // Should stay on the same page (URL should include the access code parameter)
    await expect(page).toHaveURL(/accessCode=invalidcode/);
    
    // Should still show the access code input (indicating validation failed)
    await expect(accessCodeInput).toBeVisible();
  });

  test('should clear access code when clear button is clicked', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    
    // Enter an access code
    await accessCodeInput.fill('testcode123');
    await expect(accessCodeInput).toHaveValue('testcode123');
    
    // Click the clear button (X)
    await page.getByRole('button', { name: 'Clear access code' }).click();
    
    // Input should be cleared
    await expect(accessCodeInput).toHaveValue('');
  });

  test('should show loading state when validating access code', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    const button = page.getByRole('button', { name: "Let's go!" });
    
    // Enter an access code
    await accessCodeInput.fill('testcode123');
    
    // Click the button
    await button.click();
    
    // Should show some kind of loading or processing state
    // This might be a spinner, disabled button, or loading text
    // We'll check that the button becomes disabled during processing
    await expect(button).toBeDisabled();
  });

  test('should preserve access code in URL after navigation', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    const button = page.getByRole('button', { name: "Let's go!" });
    
    // Enter an access code
    await accessCodeInput.fill('myaccesscode');
    await button.click();
    
    // URL should contain the access code
    await expect(page).toHaveURL(/accessCode=myaccesscode/);
  });

  test('should handle empty access code submission', async ({ page }) => {
    const button = page.getByRole('button', { name: "Let's go!" });
    
    // Button should be disabled when no access code is entered
    await expect(button).toBeDisabled();
  });

  test('should handle special characters in access code', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    const button = page.getByRole('button', { name: "Let's go!" });
    
    // Enter an access code with special characters
    const specialCode = 'test-code_123!@#';
    await accessCodeInput.fill(specialCode);
    await button.click();
    
    // Should handle URL encoding properly
    await expect(page).toHaveURL(new RegExp(`accessCode=${encodeURIComponent(specialCode)}`));
  });

  test('should show appropriate UI elements for anonymous user', async ({ page }) => {
    // Check that we see the access code section
    await expect(page.getByText('Have an Access Code?')).toBeVisible();
    
    // Check that we see the sign up section
    await expect(page.getByText('Get 10 Free Tries')).toBeVisible();
    
    // Check that we don't see authenticated user elements
    await expect(page.getByText('History')).not.toBeVisible();
    await expect(page.getByText('Account')).not.toBeVisible();
  });

  test('should have proper accessibility attributes', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    
    // Check that the input has proper accessibility attributes
    await expect(accessCodeInput).toHaveAttribute('type', 'text');
    
    // Check that the button has proper accessibility
    const button = page.getByRole('button', { name: "Let's go!" });
    await expect(button).toBeVisible();
  });

  test('should handle keyboard navigation', async ({ page }) => {
    const accessCodeInput = page.getByPlaceholder('Enter your access code');
    const button = page.getByRole('button', { name: "Let's go!" });
    
    // Focus the input
    await accessCodeInput.focus();
    
    // Type an access code
    await page.keyboard.type('testcode123');
    
    // Tab to the button
    await page.keyboard.press('Tab');
    
    // Button should be focused and enabled
    await expect(button).toBeFocused();
    await expect(button).toBeEnabled();
    
    // Press Enter to submit
    await page.keyboard.press('Enter');
    
    // Should navigate (URL should change)
    await expect(page).toHaveURL(/accessCode=testcode123/);
  });
});
