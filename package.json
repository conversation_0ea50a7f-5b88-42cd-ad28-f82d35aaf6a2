{"name": "hume-emotion-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@flowglad/nextjs": "^0.10.17", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "@types/uuid": "^10.0.0", "ai": "^4.3.16", "dotenv": "^17.0.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "form-data": "^4.0.3", "hume": "^0.11.3", "next": "15.3.4", "node-fetch": "^3.3.2", "postgres": "^3.4.7", "puppeteer": "^24.11.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^30.0.4", "@playwright/test": "^1.53.2", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "typescript": "^5"}}