const puppeteer = require('puppeteer');

async function testAuthModal() {
  const browser = await puppeteer.launch({ 
    headless: false, // Set to true for headless mode
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    
    console.log('Navigating to localhost:3002...');
    await page.goto('http://localhost:3002', { waitUntil: 'networkidle2' });
    
    // Wait for the page to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('Page title:', await page.title());
    
    // Check if Sign In button exists
    const signInButton = await page.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn =>
        btn.textContent?.includes('Sign In')
      );
    });
    console.log('Sign In button found:', !!signInButton.asElement());
    
    // Try alternative selectors
    const buttons = await page.$$eval('button', buttons => 
      buttons.map(btn => btn.textContent?.trim()).filter(text => text)
    );
    console.log('All buttons on page:', buttons);
    
    // Check for loading state
    const loadingElement = await page.$('.animate-pulse');
    console.log('Loading element found:', !!loadingElement);
    
    // Wait a bit more for loading to complete
    if (loadingElement) {
      console.log('Waiting for loading to complete...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    // Try to find Sign In button again
    const signInButtons = await page.$$eval('button', buttons => 
      buttons.filter(btn => btn.textContent?.includes('Sign In')).map(btn => ({
        text: btn.textContent,
        visible: btn.offsetParent !== null,
        style: window.getComputedStyle(btn).display
      }))
    );
    console.log('Sign In buttons:', signInButtons);
    
    // Try to find the "Get 10 Free Tries" button
    const freeTriesButton = await page.evaluateHandle(() => {
      return Array.from(document.querySelectorAll('button')).find(btn =>
        btn.textContent?.includes('Get 10 Free Tries')
      );
    });
    if (freeTriesButton.asElement()) {
      console.log('Found "Get 10 Free Tries" button, clicking...');
      await freeTriesButton.asElement().click();
      
      // Wait for modal to appear
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if modal is visible
      const modal = await page.$('[role="dialog"], .fixed.inset-0');
      console.log('Modal found:', !!modal);
      
      if (modal) {
        // Check modal z-index and visibility
        const modalInfo = await page.evaluate((modal) => {
          const rect = modal.getBoundingClientRect();
          const style = window.getComputedStyle(modal);
          return {
            zIndex: style.zIndex,
            display: style.display,
            visibility: style.visibility,
            opacity: style.opacity,
            position: style.position,
            top: rect.top,
            left: rect.left,
            width: rect.width,
            height: rect.height,
            isVisible: rect.width > 0 && rect.height > 0
          };
        }, modal);
        console.log('Modal info:', modalInfo);
        
        // Take a screenshot
        await page.screenshot({ path: 'auth-modal-test.png', fullPage: true });
        console.log('Screenshot saved as auth-modal-test.png');
      }
    } else {
      console.log('Could not find "Get 10 Free Tries" button');
      
      // Take a screenshot of current state
      await page.screenshot({ path: 'page-state.png', fullPage: true });
      console.log('Screenshot saved as page-state.png');
    }
    
  } catch (error) {
    console.error('Error during test:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
testAuthModal().catch(console.error);
