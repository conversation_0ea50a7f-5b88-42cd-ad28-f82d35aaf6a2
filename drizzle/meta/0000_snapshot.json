{"id": "70d9f364-83e9-41c2-9b05-c70320f72bae", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.access_codes": {"name": "access_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "created_by_id": {"name": "created_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_general": {"name": "is_general", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "current_uses": {"name": "current_uses", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"access_codes_created_by_id_profiles_id_fk": {"name": "access_codes_created_by_id_profiles_id_fk", "tableFrom": "access_codes", "tableTo": "profiles", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"access_codes_code_unique": {"name": "access_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.analyses": {"name": "analyses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "input_text": {"name": "input_text", "type": "text", "primaryKey": false, "notNull": true}, "analysis_text": {"name": "analysis_text", "type": "text", "primaryKey": false, "notNull": true}, "analyzed_emotions": {"name": "analyzed_emotions", "type": "text", "primaryKey": false, "notNull": false}, "analysis_type": {"name": "analysis_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'original'"}, "access_code_used": {"name": "access_code_used", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"analyses_user_id_profiles_id_fk": {"name": "analyses_user_id_profiles_id_fk", "tableFrom": "analyses", "tableTo": "profiles", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "analyses_access_code_used_access_codes_id_fk": {"name": "analyses_access_code_used_access_codes_id_fk", "tableFrom": "analyses", "tableTo": "access_codes", "columnsFrom": ["access_code_used"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "analysis_id": {"name": "analysis_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "feedback_text": {"name": "feedback_text", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"feedback_analysis_id_analyses_id_fk": {"name": "feedback_analysis_id_analyses_id_fk", "tableFrom": "feedback", "tableTo": "analyses", "columnsFrom": ["analysis_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.profiles": {"name": "profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}