import { NextRequest, NextResponse } from "next/server";
import { getDbForRequest } from "@/lib/db";
import { EmotionScore, AnalysisType } from "@/types/analysis";

interface SaveResultRequest {
  inputText: string;
  analysisText: string;
  analyzedEmotions?: EmotionScore[];
  analysisType?: AnalysisType;
}

export async function POST(request: NextRequest) {
  try {
    const {
      inputText,
      analysisText,
      analyzedEmotions,
      analysisType = "original",
    }: SaveResultRequest = await request.json();

    if (!inputText || !analysisText) {
      return NextResponse.json(
        { error: "inputText and analysisText are required" },
        { status: 400 }
      );
    }

    // Get the authenticated Supabase client
    const db = await getDbForRequest(request);

    // Get the current user session to link the analysis
    const {
      data: { session },
    } = await db.auth.getSession();

    // Save the analysis to the database
    const { data: result, error } = await db
      .from("analyses")
      .insert({
        input_text: inputText,
        analysis_text: analysisText,
        analyzed_emotions: analyzedEmotions
          ? JSON.stringify(analyzedEmotions)
          : null,
        analysis_type: analysisType,
        user_id: session?.user?.id || null, // Link to user if authenticated
      })
      .select()
      .single();

    if (error) {
      console.error("Database error:", error);
      return NextResponse.json(
        { error: "Failed to save result" },
        { status: 500 }
      );
    }

    // If user is authenticated, decrement their free usage count
    if (session?.user?.id) {
      // First get current usage count
      const { data: profile } = await db
        .from("profiles")
        .select("free_usages_remaining")
        .eq("id", session.user.id)
        .single();

      if (profile && profile.free_usages_remaining > 0) {
        const { error: usageError } = await db
          .from("profiles")
          .update({
            free_usages_remaining: profile.free_usages_remaining - 1,
          })
          .eq("id", session.user.id);

        if (usageError) {
          console.error("Failed to update usage count:", usageError);
          // Don't fail the request, just log the error
        }
      }
    }

    return NextResponse.json({
      id: result.id,
      message: "Result saved successfully",
    });
  } catch (error) {
    console.error("Error saving result:", error);
    return NextResponse.json(
      { error: "Internal server error while saving result" },
      { status: 500 }
    );
  }
}
