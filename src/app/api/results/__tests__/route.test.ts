import { NextRequest } from "next/server";
import { POST } from "../route";

// Use the global mocks from jest.setup.js
const mockSupabaseClient = (global as any).mockSupabaseClient;
const mockSupabaseResponse = (global as any).mockSupabaseResponse;
const mockGetDbForRequest = (global as any).mockGetDbForRequest;

describe("/api/results", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the mock response
    mockSupabaseResponse.data = null;
    mockSupabaseResponse.error = null;
  });

  describe("POST - Save analysis result", () => {
    it("should save analysis result for anonymous user", async () => {
      // Mock no session (anonymous user)
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: { session: null },
      });

      // Mock successful analysis save
      mockSupabaseResponse.data = { id: "analysis-123" };
      mockSupabaseResponse.error = null;

      const request = new NextRequest("http://localhost:3000/api/results", {
        method: "POST",
        body: JSON.stringify({
          inputText: "Hello world",
          analysisText: "This shows positive emotions",
          analyzedEmotions: [{ name: "joy", score: 0.8 }],
          analysisType: "original",
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe("analysis-123");
      expect(data.message).toBe("Result saved successfully");

      // Verify analysis was saved
      expect(mockSupabaseClient.from).toHaveBeenCalledWith("analyses");
      expect(mockSupabaseClient.from().insert).toHaveBeenCalledWith({
        input_text: "Hello world",
        analysis_text: "This shows positive emotions",
        analyzed_emotions: JSON.stringify([{ name: "joy", score: 0.8 }]),
        analysis_type: "original",
        user_id: null,
      });

      // Should not try to update usage for anonymous user
      expect(mockSupabaseClient.from).not.toHaveBeenCalledWith("profiles");
    });

    it("should save analysis result and decrement usage for authenticated user", async () => {
      // Mock authenticated session
      mockSupabaseClient.auth.getSession.mockResolvedValue({
        data: {
          session: {
            user: { id: "user-123" },
          },
        },
      });

      // Mock successful analysis save first, then profile query
      mockSupabaseResponse.data = { id: "analysis-123" };
      mockSupabaseResponse.error = null;

      // We need to handle multiple calls - first for analysis save, then for profile query
      let callCount = 0;
      mockSupabaseClient
        .from()
        .select()
        .single.mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // First call: analysis save
            return Promise.resolve({
              data: { id: "analysis-123" },
              error: null,
            });
          } else {
            // Second call: profile query
            return Promise.resolve({
              data: { free_usages_remaining: 5 },
              error: null,
            });
          }
        });

      const request = new NextRequest("http://localhost:3000/api/results", {
        method: "POST",
        body: JSON.stringify({
          inputText: "Hello world",
          analysisText: "This shows positive emotions",
          analyzedEmotions: [{ name: "joy", score: 0.8 }],
          analysisType: "original",
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe("analysis-123");

      // Verify analysis was saved with user ID
      expect(mockSupabaseClient.from().insert).toHaveBeenCalledWith({
        input_text: "Hello world",
        analysis_text: "This shows positive emotions",
        analyzed_emotions: JSON.stringify([{ name: "joy", score: 0.8 }]),
        analysis_type: "original",
        user_id: "user-123",
      });

      // Verify usage was decremented
      expect(mockSupabaseClient.from).toHaveBeenCalledWith("profiles");
      expect(mockSupabaseClient.from().update).toHaveBeenCalledWith({
        free_usages_remaining: 4,
      });
      expect(mockSupabaseClient.from().update().eq).toHaveBeenCalledWith(
        "id",
        "user-123"
      );
    });

    it("should not decrement usage if user has no remaining usages", async () => {
      // Mock authenticated session
      mockDb.auth.getSession.mockResolvedValue({
        data: {
          session: {
            user: { id: "user-123" },
          },
        },
      });

      // Mock successful analysis save
      mockFromChain.single
        .mockResolvedValueOnce({
          data: { id: "analysis-123" },
          error: null,
        })
        // Mock profile query showing 0 remaining usages
        .mockResolvedValueOnce({
          data: { free_usages_remaining: 0 },
          error: null,
        });

      const request = new NextRequest("http://localhost:3000/api/results", {
        method: "POST",
        body: JSON.stringify({
          inputText: "Hello world",
          analysisText: "This shows positive emotions",
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe("analysis-123");

      // Verify analysis was saved
      expect(mockFromChain.insert).toHaveBeenCalled();

      // Should not update usage count since user has 0 remaining
      expect(mockFromChain.update).not.toHaveBeenCalled();
    });

    it("should return 400 for missing required fields", async () => {
      const request = new NextRequest("http://localhost:3000/api/results", {
        method: "POST",
        body: JSON.stringify({
          inputText: "Hello world",
          // Missing analysisText
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("inputText and analysisText are required");
    });

    it("should handle database errors gracefully", async () => {
      mockDb.auth.getSession.mockResolvedValue({
        data: { session: null },
      });

      // Mock database error
      mockFromChain.single.mockResolvedValue({
        data: null,
        error: { message: "Database error" },
      });

      const request = new NextRequest("http://localhost:3000/api/results", {
        method: "POST",
        body: JSON.stringify({
          inputText: "Hello world",
          analysisText: "This shows positive emotions",
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe("Failed to save result");
    });

    it("should continue if usage update fails", async () => {
      // Mock authenticated session
      mockDb.auth.getSession.mockResolvedValue({
        data: {
          session: {
            user: { id: "user-123" },
          },
        },
      });

      // Mock successful analysis save
      mockFromChain.single
        .mockResolvedValueOnce({
          data: { id: "analysis-123" },
          error: null,
        })
        // Mock profile query
        .mockResolvedValueOnce({
          data: { free_usages_remaining: 5 },
          error: null,
        });

      // Mock usage update failure
      mockFromChain.eq.mockResolvedValue({
        error: { message: "Update failed" },
      });

      const request = new NextRequest("http://localhost:3000/api/results", {
        method: "POST",
        body: JSON.stringify({
          inputText: "Hello world",
          analysisText: "This shows positive emotions",
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      // Should still succeed even if usage update fails
      expect(response.status).toBe(200);
      expect(data.id).toBe("analysis-123");
    });
  });
});
