import { GET, POST, PUT } from '../route';
import { NextRequest } from 'next/server';

// Mock the database
jest.mock('@/lib/db', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(),
      })),
    })),
  },
  getDbForRequest: jest.fn(),
}));

import { supabase, getDbForRequest } from '@/lib/db';

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockGetDbForRequest = getDbForRequest as jest.MockedFunction<typeof getDbForRequest>;

describe('/api/access-codes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET - Validate access code', () => {
    it('should validate a valid access code', async () => {
      const mockAccessCode = {
        id: 'code-123',
        code: 'validcode',
        max_uses: 1,
        current_uses: 0,
        expires_at: null,
        is_general: false,
      };

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockAccessCode,
        error: null,
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes?code=validcode');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.valid).toBe(true);
      expect(data.accessCode.code).toBe('validcode');
    });

    it('should reject invalid access code', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { message: 'Not found' },
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes?code=invalidcode');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.valid).toBe(false);
      expect(data.error).toBe('Invalid access code');
    });

    it('should reject expired access code', async () => {
      const expiredDate = new Date();
      expiredDate.setDate(expiredDate.getDate() - 1);

      const mockAccessCode = {
        id: 'code-123',
        code: 'expiredcode',
        max_uses: 1,
        current_uses: 0,
        expires_at: expiredDate.toISOString(),
        is_general: false,
      };

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockAccessCode,
        error: null,
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes?code=expiredcode');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.valid).toBe(false);
      expect(data.error).toBe('Access code has expired');
    });

    it('should reject exhausted access code', async () => {
      const mockAccessCode = {
        id: 'code-123',
        code: 'exhaustedcode',
        max_uses: 1,
        current_uses: 1,
        expires_at: null,
        is_general: false,
      };

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockAccessCode,
        error: null,
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes?code=exhaustedcode');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.valid).toBe(false);
      expect(data.error).toBe('Access code has reached maximum uses');
    });

    it('should return 400 when no code provided', async () => {
      const request = new NextRequest('http://localhost:3000/api/access-codes');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Access code is required');
    });
  });

  describe('POST - Create access code', () => {
    const mockAuthenticatedDb = {
      auth: {
        getSession: jest.fn(),
      },
      from: jest.fn(() => ({
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn(),
          })),
        })),
      })),
    };

    beforeEach(() => {
      mockGetDbForRequest.mockResolvedValue(mockAuthenticatedDb as any);
    });

    it('should create access code for authenticated user', async () => {
      mockAuthenticatedDb.auth.getSession.mockResolvedValue({
        data: {
          session: {
            user: { id: 'user-123' }
          }
        }
      });

      const mockNewAccessCode = {
        id: 'code-123',
        code: 'newcode123',
      };

      mockAuthenticatedDb.from().insert().select().single.mockResolvedValue({
        data: mockNewAccessCode,
        error: null,
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes', {
        method: 'POST',
        body: JSON.stringify({
          maxUses: 1,
          expiresAt: null,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.id).toBe('code-123');
      expect(data.code).toBe('newcode123');
      expect(data.message).toBe('Access code created successfully');
    });

    it('should return 401 for unauthenticated user', async () => {
      mockAuthenticatedDb.auth.getSession.mockResolvedValue({
        data: { session: null }
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes', {
        method: 'POST',
        body: JSON.stringify({
          maxUses: 1,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Authentication required');
    });
  });

  describe('PUT - Use access code', () => {
    it('should increment usage count for valid code', async () => {
      const mockAccessCode = {
        id: 'code-123',
        code: 'validcode',
        max_uses: 1,
        current_uses: 0,
        expires_at: null,
      };

      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: mockAccessCode,
        error: null,
      });

      mockSupabase.from().update().eq.mockResolvedValue({
        error: null,
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes', {
        method: 'PUT',
        body: JSON.stringify({
          code: 'validcode',
        }),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Access code used successfully');
      expect(data.remainingUses).toBe(0);
    });

    it('should return 404 for invalid code', async () => {
      mockSupabase.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { message: 'Not found' },
      });

      const request = new NextRequest('http://localhost:3000/api/access-codes', {
        method: 'PUT',
        body: JSON.stringify({
          code: 'invalidcode',
        }),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Invalid access code');
    });

    it('should return 400 when no code provided', async () => {
      const request = new NextRequest('http://localhost:3000/api/access-codes', {
        method: 'PUT',
        body: JSON.stringify({}),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Access code is required');
    });
  });
});
