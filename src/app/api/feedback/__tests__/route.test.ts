import { POST } from '../route';
import { NextRequest } from 'next/server';

// Mock the database
jest.mock('@/lib/db', () => ({
  getDbForRequest: jest.fn(),
}));

import { getDbForRequest } from '@/lib/db';

const mockGetDbForRequest = getDbForRequest as jest.MockedFunction<typeof getDbForRequest>;

describe('/api/feedback', () => {
  const mockDb = {
    from: jest.fn(() => ({
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
    })),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetDbForRequest.mockResolvedValue(mockDb as any);
  });

  it('should save feedback successfully', async () => {
    const mockFeedbackRecord = {
      id: 'feedback-123',
      analysis_id: 'analysis-123',
      email: '<EMAIL>',
      feedback_text: 'Great analysis!',
      created_at: new Date().toISOString(),
    };

    mockDb.from().insert().select().single.mockResolvedValue({
      data: mockFeedbackRecord,
      error: null,
    });

    const requestBody = {
      resultId: 'analysis-123',
      email: '<EMAIL>',
      feedback: 'Great analysis!',
    };

    const request = new NextRequest('http://localhost:3000/api/feedback', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.message).toBe('Feedback saved successfully');
    expect(data.id).toBe('feedback-123');

    expect(mockDb.from).toHaveBeenCalledWith('feedback');
    expect(mockDb.from().insert).toHaveBeenCalledWith({
      analysis_id: 'analysis-123',
      email: '<EMAIL>',
      feedback_text: 'Great analysis!',
    });
  });

  it('should save feedback without email', async () => {
    const mockFeedbackRecord = {
      id: 'feedback-123',
      analysis_id: null,
      email: null,
      feedback_text: 'Anonymous feedback',
      created_at: new Date().toISOString(),
    };

    mockDb.from().insert().select().single.mockResolvedValue({
      data: mockFeedbackRecord,
      error: null,
    });

    const requestBody = {
      feedback: 'Anonymous feedback',
    };

    const request = new NextRequest('http://localhost:3000/api/feedback', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.message).toBe('Feedback saved successfully');

    expect(mockDb.from().insert).toHaveBeenCalledWith({
      analysis_id: null,
      email: null,
      feedback_text: 'Anonymous feedback',
    });
  });

  it('should return 400 when feedback is empty', async () => {
    const requestBody = {
      feedback: '',
    };

    const request = new NextRequest('http://localhost:3000/api/feedback', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Feedback text is required');
  });

  it('should return 400 when feedback is only whitespace', async () => {
    const requestBody = {
      feedback: '   \n\t   ',
    };

    const request = new NextRequest('http://localhost:3000/api/feedback', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Feedback text is required');
  });

  it('should return 400 when feedback is missing', async () => {
    const requestBody = {
      email: '<EMAIL>',
    };

    const request = new NextRequest('http://localhost:3000/api/feedback', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Feedback text is required');
  });

  it('should handle database errors', async () => {
    mockDb.from().insert().select().single.mockResolvedValue({
      data: null,
      error: { message: 'Database error' },
    });

    const requestBody = {
      feedback: 'Test feedback',
    };

    const request = new NextRequest('http://localhost:3000/api/feedback', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to save feedback');
  });

  it('should trim whitespace from feedback', async () => {
    const mockFeedbackRecord = {
      id: 'feedback-123',
      analysis_id: null,
      email: null,
      feedback_text: 'Trimmed feedback',
      created_at: new Date().toISOString(),
    };

    mockDb.from().insert().select().single.mockResolvedValue({
      data: mockFeedbackRecord,
      error: null,
    });

    const requestBody = {
      feedback: '  Trimmed feedback  \n',
    };

    const request = new NextRequest('http://localhost:3000/api/feedback', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);

    expect(response.status).toBe(200);
    expect(mockDb.from().insert).toHaveBeenCalledWith({
      analysis_id: null,
      email: null,
      feedback_text: 'Trimmed feedback',
    });
  });
});
