import { POST } from '../route';
import { NextRequest } from 'next/server';

// Mock the analysis configs
jest.mock('@/lib/analysis-configs', () => ({
  getInsightGenerationConfig: jest.fn(() => ({
    systemPrompt: 'Test system prompt',
    useSentenceLevel: false,
  })),
}));

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('/api/generate-insights', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.OPENROUTER_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    delete process.env.OPENROUTER_API_KEY;
  });

  it('should generate insights successfully', async () => {
    const mockOpenRouterResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        choices: [{
          message: {
            content: 'Generated insights about emotions'
          }
        }]
      })
    };

    mockFetch.mockResolvedValue(mockOpenRouterResponse);

    const requestBody = {
      transcript: 'Hello world, I am feeling great today!',
      topEmotions: [
        { name: 'joy', score: 0.8 },
        { name: 'excitement', score: 0.6 }
      ],
      analysisType: 'original'
    };

    const request = new NextRequest('http://localhost:3000/api/generate-insights', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.insights).toBe('Generated insights about emotions');
    expect(mockFetch).toHaveBeenCalledWith(
      'https://openrouter.ai/api/v1/chat/completions',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Authorization': 'Bearer test-api-key',
          'Content-Type': 'application/json',
        }),
      })
    );
  });

  it('should return 400 when transcript is missing', async () => {
    const requestBody = {
      topEmotions: [{ name: 'joy', score: 0.8 }],
    };

    const request = new NextRequest('http://localhost:3000/api/generate-insights', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Transcript and emotions are required');
  });

  it('should return 400 when emotions are missing', async () => {
    const requestBody = {
      transcript: 'Hello world',
      topEmotions: [],
    };

    const request = new NextRequest('http://localhost:3000/api/generate-insights', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Transcript and emotions are required');
  });

  it('should return 500 when OpenRouter API key is not configured', async () => {
    delete process.env.OPENROUTER_API_KEY;

    const requestBody = {
      transcript: 'Hello world',
      topEmotions: [{ name: 'joy', score: 0.8 }],
    };

    const request = new NextRequest('http://localhost:3000/api/generate-insights', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('OpenRouter API key not configured');
  });

  it('should handle OpenRouter API errors', async () => {
    const mockOpenRouterResponse = {
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
    };

    mockFetch.mockResolvedValue(mockOpenRouterResponse);

    const requestBody = {
      transcript: 'Hello world',
      topEmotions: [{ name: 'joy', score: 0.8 }],
    };

    const request = new NextRequest('http://localhost:3000/api/generate-insights', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to generate insights');
  });

  it('should handle sentence-level analysis', async () => {
    const mockOpenRouterResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        choices: [{
          message: {
            content: 'Sentence-level insights'
          }
        }]
      })
    };

    mockFetch.mockResolvedValue(mockOpenRouterResponse);

    const requestBody = {
      transcript: 'Hello world. I am feeling great.',
      topEmotions: [{ name: 'joy', score: 0.8 }],
      analysisType: 'sentence-level',
      sentenceEmotions: [
        {
          sentence: 'Hello world.',
          emotions: [{ name: 'neutral', score: 0.7 }]
        },
        {
          sentence: 'I am feeling great.',
          emotions: [{ name: 'joy', score: 0.9 }]
        }
      ]
    };

    const request = new NextRequest('http://localhost:3000/api/generate-insights', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.insights).toBe('Sentence-level insights');
  });

  it('should handle network errors', async () => {
    mockFetch.mockRejectedValue(new Error('Network error'));

    const requestBody = {
      transcript: 'Hello world',
      topEmotions: [{ name: 'joy', score: 0.8 }],
    };

    const request = new NextRequest('http://localhost:3000/api/generate-insights', {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to generate insights');
  });
});
