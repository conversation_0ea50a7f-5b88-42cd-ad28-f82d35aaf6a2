export default function Billing() {
  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Billing & Subscription</h1>
        <p className="mt-2 text-gray-600">
          Manage your subscription and billing information
        </p>
      </div>

      <div className="bg-white/70 backdrop-blur-sm shadow-sm rounded-2xl border border-white/20 p-8 mb-8">
        <div className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-2xl p-8 border border-blue-100/30">
          <div className="flex items-center justify-center mb-6">
            <span className="bg-blue-100/70 text-blue-700 text-xs font-medium px-4 py-2 rounded-full">
              Alpha User Pricing
            </span>
          </div>
          <div className="text-center">
            <div className="text-4xl font-light text-gray-800 mb-4">
              $20<span className="text-xl font-light text-gray-500">/month</span>
            </div>
            <p className="text-gray-600 mb-6">Unlimited voice emotion analysis (new users get 10 free analyses)</p>
            <p className="text-sm text-gray-500 mb-6">
              Billing integration with Flowglad is set up. The checkout functionality will be completed once API keys are configured.
            </p>
            <button
              disabled
              className="w-full bg-gray-400 text-white px-6 py-3 rounded-md font-medium cursor-not-allowed"
            >
              Subscribe Now - $20/month (Coming Soon)
            </button>
            <p className="text-xs text-gray-500 mt-3">
              Cancel anytime • No long-term commitment • Alpha pricing guaranteed
            </p>
          </div>
        </div>
      </div>

      <div className="text-center">
        <a href="/account" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
          ← Back to Account
        </a>
      </div>
    </div>
  );
}
