/**
 * Error handling tests for audio recording and processing
 * Tests microphone permissions, browser compatibility, and audio format issues
 */

import { AudioRecorder, convertBlobToBase64 } from '@/lib/audio-utils';

describe('Audio Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Microphone Permission Errors', () => {
    it('should handle permission denied error', async () => {
      const mockGetUserMedia = jest.fn().mockRejectedValue(
        new DOMException('Permission denied', 'NotAllowedError')
      );

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });

    it('should handle device not found error', async () => {
      const mockGetUserMedia = jest.fn().mockRejectedValue(
        new DOMException('Device not found', 'NotFoundError')
      );

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });

    it('should handle device not readable error', async () => {
      const mockGetUserMedia = jest.fn().mockRejectedValue(
        new DOMException('Device not readable', 'NotReadableError')
      );

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });

    it('should handle overconstrained error', async () => {
      const mockGetUserMedia = jest.fn().mockRejectedValue(
        new DOMException('Constraints not satisfied', 'OverconstrainedError')
      );

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });
  });

  describe('MediaRecorder Errors', () => {
    it('should handle MediaRecorder not supported', async () => {
      // Mock MediaRecorder as undefined (not supported)
      const originalMediaRecorder = global.MediaRecorder;
      (global as any).MediaRecorder = undefined;

      const mockGetUserMedia = jest.fn().mockResolvedValue({
        getTracks: jest.fn(() => [{ stop: jest.fn() }])
      });

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );

      // Restore MediaRecorder
      global.MediaRecorder = originalMediaRecorder;
    });

    it('should handle MediaRecorder constructor error', async () => {
      const mockMediaStream = {
        getTracks: jest.fn(() => [{ stop: jest.fn() }])
      };

      const mockGetUserMedia = jest.fn().mockResolvedValue(mockMediaStream);

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      // Mock MediaRecorder to throw on construction
      global.MediaRecorder = jest.fn(() => {
        throw new Error('MediaRecorder construction failed');
      }) as any;

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });

    it('should handle MediaRecorder start error', async () => {
      const mockMediaStream = {
        getTracks: jest.fn(() => [{ stop: jest.fn() }])
      };

      const mockGetUserMedia = jest.fn().mockResolvedValue(mockMediaStream);

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const mockMediaRecorder = {
        start: jest.fn(() => {
          throw new Error('MediaRecorder start failed');
        }),
        stop: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        state: 'inactive',
      };

      global.MediaRecorder = jest.fn(() => mockMediaRecorder) as any;

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });
  });

  describe('Recording State Errors', () => {
    it('should handle stop recording when not recording', async () => {
      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.stopRecording()).rejects.toThrow(
        'No recording in progress'
      );
    });

    it('should handle multiple start recording calls', async () => {
      const mockMediaStream = {
        getTracks: jest.fn(() => [{ stop: jest.fn() }])
      };

      const mockGetUserMedia = jest.fn().mockResolvedValue(mockMediaStream);

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const mockMediaRecorder = {
        start: jest.fn(),
        stop: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        state: 'recording',
      };

      global.MediaRecorder = jest.fn(() => mockMediaRecorder) as any;

      const audioRecorder = new AudioRecorder();

      // First call should succeed
      await audioRecorder.startRecording();

      // Second call should be ignored or handled gracefully
      // The implementation should check if already recording
      expect(mockGetUserMedia).toHaveBeenCalledTimes(1);
    });
  });

  describe('Audio Conversion Errors', () => {
    it('should handle FileReader error during blob conversion', async () => {
      const mockBlob = new Blob(['test data'], { type: 'audio/webm' });
      const mockError = new Error('FileReader failed');

      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: null,
      };

      global.FileReader = jest.fn(() => mockFileReader) as any;

      const conversionPromise = convertBlobToBase64(mockBlob);

      // Simulate FileReader error
      setTimeout(() => {
        if (mockFileReader.onerror) {
          mockFileReader.onerror(mockError);
        }
      }, 0);

      await expect(conversionPromise).rejects.toBe(mockError);
    });

    it('should handle null result from FileReader', async () => {
      const mockBlob = new Blob(['test data'], { type: 'audio/webm' });

      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: null, // Null result
      };

      global.FileReader = jest.fn(() => mockFileReader) as any;

      const conversionPromise = convertBlobToBase64(mockBlob);

      // Simulate FileReader success but with null result
      setTimeout(() => {
        if (mockFileReader.onload) {
          mockFileReader.onload();
        }
      }, 0);

      await expect(conversionPromise).rejects.toThrow('Failed to convert blob to base64');
    });

    it('should handle invalid data URL format', async () => {
      const mockBlob = new Blob(['test data'], { type: 'audio/webm' });

      const mockFileReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: 'invalid-data-url', // Invalid format
      };

      global.FileReader = jest.fn(() => mockFileReader) as any;

      const conversionPromise = convertBlobToBase64(mockBlob);

      // Simulate FileReader success with invalid result
      setTimeout(() => {
        if (mockFileReader.onload) {
          mockFileReader.onload();
        }
      }, 0);

      await expect(conversionPromise).rejects.toThrow('Invalid data URL format');
    });
  });

  describe('Browser Compatibility', () => {
    it('should handle missing navigator.mediaDevices', async () => {
      // Mock missing mediaDevices API
      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: undefined,
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });

    it('should handle missing getUserMedia', async () => {
      // Mock mediaDevices without getUserMedia
      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: {},
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });

    it('should handle unsupported audio constraints', async () => {
      const mockGetUserMedia = jest.fn().mockRejectedValue(
        new DOMException('Unsupported constraint', 'ConstraintNotSatisfiedError')
      );

      Object.defineProperty(navigator, 'mediaDevices', {
        writable: true,
        value: { getUserMedia: mockGetUserMedia },
      });

      const audioRecorder = new AudioRecorder();

      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });
  });
});
