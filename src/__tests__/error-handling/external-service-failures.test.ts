/**
 * Error handling tests for external service failures
 * Tests how the application handles failures from Hume API, OpenRouter API, and Supabase
 */

import { AnalysisFactory } from '@/lib/analysis-factory';

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('External Service Failure Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('OpenRouter API Failures', () => {
    it('should handle network timeout errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network timeout'));

      const analysisData = {
        transcript: 'Test transcript',
        analyzedEmotions: [{ name: 'joy', score: 0.8 }]
      };

      await expect(
        AnalysisFactory.generateInsights(analysisData, 'original')
      ).rejects.toThrow('Failed to generate insights');
    });

    it('should handle 500 server errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      } as Response);

      const analysisData = {
        transcript: 'Test transcript',
        analyzedEmotions: [{ name: 'joy', score: 0.8 }]
      };

      await expect(
        AnalysisFactory.generateInsights(analysisData, 'original')
      ).rejects.toThrow('Failed to generate insights');
    });

    it('should handle 429 rate limit errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response);

      const analysisData = {
        transcript: 'Test transcript',
        analyzedEmotions: [{ name: 'joy', score: 0.8 }]
      };

      await expect(
        AnalysisFactory.generateInsights(analysisData, 'original')
      ).rejects.toThrow('Failed to generate insights');
    });

    it('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockRejectedValue(new Error('Invalid JSON'))
      } as any);

      const analysisData = {
        transcript: 'Test transcript',
        analyzedEmotions: [{ name: 'joy', score: 0.8 }]
      };

      await expect(
        AnalysisFactory.generateInsights(analysisData, 'original')
      ).rejects.toThrow('Failed to generate insights');
    });

    it('should handle missing API key', async () => {
      // Temporarily remove the API key
      const originalKey = process.env.OPENROUTER_API_KEY;
      delete process.env.OPENROUTER_API_KEY;

      const analysisData = {
        transcript: 'Test transcript',
        analyzedEmotions: [{ name: 'joy', score: 0.8 }]
      };

      await expect(
        AnalysisFactory.generateInsights(analysisData, 'original')
      ).rejects.toThrow('OpenRouter API key not configured');

      // Restore the API key
      if (originalKey) {
        process.env.OPENROUTER_API_KEY = originalKey;
      }
    });
  });

  describe('Hume API Response Processing', () => {
    it('should handle empty Hume responses gracefully', () => {
      const emptyResponse = { results: [] };
      
      const result = AnalysisFactory.processHumeResponse(
        emptyResponse,
        'original',
        0.5,
        5
      );

      expect(result.transcript).toBe('');
      expect(result.emotions).toHaveLength(0);
      expect(result.analyzedEmotions).toHaveLength(0);
    });

    it('should handle malformed Hume responses', () => {
      const malformedResponse = {
        results: [{
          results: {
            predictions: [{
              models: {
                // Missing prosody data
              }
            }]
          }
        }]
      };

      const result = AnalysisFactory.processHumeResponse(
        malformedResponse,
        'original',
        0.5,
        5
      );

      expect(result.transcript).toBe('');
      expect(result.emotions).toHaveLength(0);
      expect(result.analyzedEmotions).toHaveLength(0);
    });

    it('should handle missing emotion scores', () => {
      const responseWithMissingScores = {
        results: [{
          results: {
            predictions: [{
              models: {
                prosody: {
                  groupedPredictions: [{
                    predictions: [{
                      text: "Hello world",
                      emotions: [
                        { name: "joy" }, // Missing score
                        { name: "sadness", score: 0.6 }
                      ],
                      time: { begin: 0, end: 2 }
                    }]
                  }]
                }
              }
            }]
          }
        }],
        transcript: "Hello world"
      };

      const result = AnalysisFactory.processHumeResponse(
        responseWithMissingScores,
        'original',
        0.5,
        5
      );

      // Should only include emotions with valid scores
      expect(result.emotions.length).toBeGreaterThan(0);
      expect(result.emotions.every(emotion => typeof emotion.score === 'number')).toBe(true);
    });

    it('should handle null or undefined emotion arrays', () => {
      const responseWithNullEmotions = {
        results: [{
          results: {
            predictions: [{
              models: {
                prosody: {
                  groupedPredictions: [{
                    predictions: [{
                      text: "Hello world",
                      emotions: null,
                      time: { begin: 0, end: 2 }
                    }]
                  }]
                }
              }
            }]
          }
        }],
        transcript: "Hello world"
      };

      const result = AnalysisFactory.processHumeResponse(
        responseWithNullEmotions,
        'original',
        0.5,
        5
      );

      expect(result.transcript).toBe('Hello world');
      expect(result.emotions).toHaveLength(0);
      expect(result.analyzedEmotions).toHaveLength(0);
    });
  });

  describe('Input Validation', () => {
    it('should handle empty transcript', async () => {
      const analysisData = {
        transcript: '',
        analyzedEmotions: [{ name: 'joy', score: 0.8 }]
      };

      // Should still attempt to generate insights but may fail gracefully
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          choices: [{ message: { content: 'No meaningful analysis possible for empty transcript.' } }]
        })
      } as any);

      const result = await AnalysisFactory.generateInsights(analysisData, 'original');
      expect(result).toBe('No meaningful analysis possible for empty transcript.');
    });

    it('should handle empty emotions array', async () => {
      const analysisData = {
        transcript: 'Hello world',
        analyzedEmotions: []
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValue({
          choices: [{ message: { content: 'No emotions detected for analysis.' } }]
        })
      } as any);

      const result = await AnalysisFactory.generateInsights(analysisData, 'original');
      expect(result).toBe('No emotions detected for analysis.');
    });

    it('should handle extremely long transcript', () => {
      const longTranscript = 'word '.repeat(10000); // Very long transcript
      const response = {
        results: [{
          results: {
            predictions: [{
              models: {
                prosody: {
                  groupedPredictions: [{
                    predictions: [{
                      text: longTranscript,
                      emotions: [{ name: "neutral", score: 0.5 }],
                      time: { begin: 0, end: 100 }
                    }]
                  }]
                }
              }
            }]
          }
        }],
        transcript: longTranscript
      };

      const result = AnalysisFactory.processHumeResponse(
        response,
        'original',
        0.3,
        5
      );

      expect(result.transcript).toBe(longTranscript);
      expect(result.emotions).toHaveLength(1);
    });

    it('should handle invalid emotion scores', () => {
      const responseWithInvalidScores = {
        results: [{
          results: {
            predictions: [{
              models: {
                prosody: {
                  groupedPredictions: [{
                    predictions: [{
                      text: "Hello world",
                      emotions: [
                        { name: "joy", score: "invalid" }, // Invalid score type
                        { name: "sadness", score: -1 }, // Negative score
                        { name: "anger", score: 2 }, // Score > 1
                        { name: "fear", score: 0.5 } // Valid score
                      ],
                      time: { begin: 0, end: 2 }
                    }]
                  }]
                }
              }
            }]
          }
        }],
        transcript: "Hello world"
      };

      const result = AnalysisFactory.processHumeResponse(
        responseWithInvalidScores,
        'original',
        0.3,
        5
      );

      // Should filter out invalid scores and keep only valid ones
      expect(result.emotions.length).toBeGreaterThan(0);
      expect(result.emotions.every(emotion => 
        typeof emotion.score === 'number' && 
        emotion.score >= 0 && 
        emotion.score <= 1
      )).toBe(true);
    });
  });
});
