/**
 * Integration test for the complete audio analysis flow
 * Tests the interaction between audio recording, emotion analysis, and result generation
 */

import { AudioRecorder, convertBlobToBase64 } from "@/lib/audio-utils";
import { AnalysisFactory } from "@/lib/analysis-factory";

// Mock only external APIs, not our own code
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock browser APIs that aren't available in test environment
const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  state: "inactive",
  ondataavailable: null,
  onstop: null,
};

const mockMediaStream = {
  getTracks: jest.fn(() => [{ stop: jest.fn() }, { stop: jest.fn() }]),
};

global.MediaRecorder = jest.fn(() => mockMediaRecorder) as any;

Object.defineProperty(navigator, "mediaDevices", {
  writable: true,
  value: {
    getUserMedia: jest.fn().mockResolvedValue(mockMediaStream),
  },
});

global.FileReader = jest.fn(() => ({
  readAsDataURL: jest.fn(),
  onload: null,
  onerror: null,
  result: "data:audio/webm;base64,dGVzdCBhdWRpbyBkYXRh", // "test audio data" in base64
})) as any;

describe("Audio Analysis Integration Flow", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockMediaRecorder.state = "inactive";
  });

  it("should complete full audio recording and analysis flow", async () => {
    // Mock Hume API response
    const mockHumeResponse = {
      results: [
        {
          results: {
            predictions: [
              {
                models: {
                  prosody: {
                    groupedPredictions: [
                      {
                        predictions: [
                          {
                            text: "I am feeling really happy today",
                            emotions: [
                              { name: "joy", score: 0.85 },
                              { name: "contentment", score: 0.72 },
                              { name: "excitement", score: 0.68 },
                            ],
                            time: { begin: 0, end: 3 },
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
      ],
      transcript: "I am feeling really happy today",
    };

    // Mock OpenRouter API response for insights
    const mockInsightsResponse = {
      choices: [
        {
          message: {
            content:
              "Based on your voice analysis, you're experiencing strong positive emotions, particularly joy and contentment. This suggests you're in a great emotional state today!",
          },
        },
      ],
    };

    // Setup fetch mocks
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockHumeResponse),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockInsightsResponse),
      } as Response);

    // Step 1: Record audio
    const audioRecorder = new AudioRecorder();
    await audioRecorder.startRecording();

    expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        sampleRate: 44100,
      },
    });
    expect(mockMediaRecorder.start).toHaveBeenCalled();

    // Simulate recording completion
    const mockBlob = new Blob(["test audio data"], { type: "audio/webm" });
    mockMediaRecorder.state = "recording";

    const stopPromise = audioRecorder.stopRecording();

    // Simulate the stop event with data
    if (mockMediaRecorder.onstop) {
      mockMediaRecorder.onstop();
    }

    const audioBlob = await stopPromise;
    expect(audioBlob).toBeInstanceOf(Blob);

    // Step 2: Convert audio to base64
    const base64Promise = convertBlobToBase64(audioBlob);

    // Simulate FileReader success
    const mockFileReader = (global.FileReader as jest.Mock).mock.instances[0];
    setTimeout(() => {
      if (mockFileReader.onload) {
        mockFileReader.onload();
      }
    }, 0);

    const base64Audio = await base64Promise;
    expect(base64Audio).toBe("dGVzdCBhdWRpbyBkYXRh");

    // Step 3: Process Hume response
    const processedResponse = AnalysisFactory.processHumeResponse(
      mockHumeResponse,
      "original",
      0.5, // threshold
      5 // maxEmotions
    );

    expect(processedResponse.transcript).toBe(
      "I am feeling really happy today"
    );
    expect(processedResponse.emotions).toHaveLength(3);
    expect(processedResponse.emotions[0].name).toBe("joy");
    expect(processedResponse.emotions[0].score).toBe(0.85);
    expect(processedResponse.analyzedEmotions).toHaveLength(2); // joy and contentment above 0.5 threshold

    // Step 4: Generate insights
    const insights = await AnalysisFactory.generateInsights(
      processedResponse,
      "original"
    );

    expect(insights).toBe(
      "Based on your voice analysis, you're experiencing strong positive emotions, particularly joy and contentment. This suggests you're in a great emotional state today!"
    );

    // Step 5: Create final analysis data
    const finalAnalysisData = AnalysisFactory.createAnalysisData(
      processedResponse,
      insights,
      "original"
    );

    expect(finalAnalysisData).toEqual({
      transcript: "I am feeling really happy today",
      emotions: processedResponse.emotions,
      analyzedEmotions: processedResponse.analyzedEmotions,
      sentenceEmotions: undefined,
      insights: insights,
      analysisType: "original",
    });

    // Verify API calls were made correctly
    expect(mockFetch).toHaveBeenCalledTimes(1); // Only insights call in this test
    expect(mockFetch).toHaveBeenCalledWith("/api/generate-insights", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        transcript: "I am feeling really happy today",
        topEmotions: processedResponse.analyzedEmotions,
        analysisType: "original",
        sentenceEmotions: undefined,
      }),
    });
  });

  it("should handle sentence-level analysis flow", async () => {
    const mockSentenceLevelResponse = {
      results: [
        {
          results: {
            predictions: [
              {
                models: {
                  prosody: {
                    groupedPredictions: [
                      {
                        predictions: [
                          {
                            text: "Hello there.",
                            emotions: [{ name: "neutral", score: 0.7 }],
                            time: { begin: 0, end: 1 },
                          },
                          {
                            text: "I am so excited!",
                            emotions: [{ name: "excitement", score: 0.9 }],
                            time: { begin: 1, end: 3 },
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
      ],
      transcript: "Hello there. I am so excited!",
    };

    const processedResponse = AnalysisFactory.processHumeResponse(
      mockSentenceLevelResponse,
      "sentence-level",
      0.3,
      5
    );

    expect(processedResponse.sentenceEmotions).toBeDefined();
    expect(processedResponse.sentenceEmotions).toHaveLength(2);
    expect(processedResponse.sentenceEmotions![0].sentence).toBe(
      "Hello there."
    );
    expect(processedResponse.sentenceEmotions![1].sentence).toBe(
      "I am so excited!"
    );
  });

  it("should handle errors gracefully in the flow", async () => {
    // Test audio recording error
    const getUserMediaError = new Error("Permission denied");
    (navigator.mediaDevices.getUserMedia as jest.Mock).mockRejectedValueOnce(
      getUserMediaError
    );

    const audioRecorder = new AudioRecorder();

    await expect(audioRecorder.startRecording()).rejects.toThrow(
      "Failed to start recording. Please check microphone permissions."
    );
  });

  it("should handle API errors in insights generation", async () => {
    mockFetch.mockRejectedValueOnce(new Error("Network error"));

    const mockAnalysisData = {
      transcript: "Test transcript",
      analyzedEmotions: [{ name: "joy", score: 0.8 }],
    };

    await expect(
      AnalysisFactory.generateInsights(mockAnalysisData, "original")
    ).rejects.toThrow("Failed to generate insights");
  });

  it("should handle empty Hume response", async () => {
    const emptyResponse = { results: [] };

    const processedResponse = AnalysisFactory.processHumeResponse(
      emptyResponse,
      "original",
      0.5,
      5
    );

    expect(processedResponse.transcript).toBe("");
    expect(processedResponse.emotions).toHaveLength(0);
    expect(processedResponse.analyzedEmotions).toHaveLength(0);
  });
});
