/**
 * Performance tests for emotion analysis
 * Tests response times, memory usage, and concurrent user handling
 */

import { AnalysisFactory } from '@/lib/analysis-factory';
import { AudioRecorder, convertBlobToBase64 } from '@/lib/audio-utils';

// Mock fetch for controlled performance testing
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock browser APIs
const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  state: 'inactive',
  ondataavailable: null,
  onstop: null,
};

const mockMediaStream = {
  getTracks: jest.fn(() => [{ stop: jest.fn() }]),
};

global.MediaRecorder = jest.fn(() => mockMediaRecorder) as any;
Object.defineProperty(navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: jest.fn().mockResolvedValue(mockMediaStream),
  },
});

global.FileReader = jest.fn(() => ({
  readAsDataURL: jest.fn(),
  onload: null,
  onerror: null,
  result: 'data:audio/webm;base64,dGVzdCBhdWRpbyBkYXRh',
})) as any;

describe('Analysis Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockMediaRecorder.state = 'inactive';
  });

  describe('Response Time Measurements', () => {
    it('should process small Hume response within acceptable time', () => {
      const startTime = performance.now();

      const smallResponse = {
        results: [{
          results: {
            predictions: [{
              models: {
                prosody: {
                  groupedPredictions: [{
                    predictions: [{
                      text: "Hello world",
                      emotions: [
                        { name: "joy", score: 0.8 },
                        { name: "neutral", score: 0.6 }
                      ],
                      time: { begin: 0, end: 2 }
                    }]
                  }]
                }
              }
            }]
          }
        }],
        transcript: "Hello world"
      };

      const result = AnalysisFactory.processHumeResponse(
        smallResponse,
        'original',
        0.3,
        5
      );

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(result.transcript).toBe('Hello world');
      expect(processingTime).toBeLessThan(100); // Should process in under 100ms
    });

    it('should process large Hume response within reasonable time', () => {
      const startTime = performance.now();

      // Create a large response with many emotions and predictions
      const largeEmotions = Array.from({ length: 50 }, (_, i) => ({
        name: `emotion${i}`,
        score: Math.random()
      }));

      const largePredictions = Array.from({ length: 20 }, (_, i) => ({
        text: `Sentence ${i} with some content`,
        emotions: largeEmotions,
        time: { begin: i, end: i + 1 }
      }));

      const largeResponse = {
        results: [{
          results: {
            predictions: [{
              models: {
                prosody: {
                  groupedPredictions: [{
                    predictions: largePredictions
                  }]
                }
              }
            }]
          }
        }],
        transcript: largePredictions.map(p => p.text).join(' ')
      };

      const result = AnalysisFactory.processHumeResponse(
        largeResponse,
        'original',
        0.3,
        10
      );

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(result.emotions.length).toBeGreaterThan(0);
      expect(processingTime).toBeLessThan(1000); // Should process in under 1 second
    });

    it('should handle insights generation within timeout', async () => {
      const startTime = performance.now();

      // Mock a response that takes some time but not too long
      mockFetch.mockImplementation(() => 
        new Promise(resolve => {
          setTimeout(() => {
            resolve({
              ok: true,
              json: () => Promise.resolve({
                choices: [{ message: { content: 'Generated insights' } }]
              })
            } as Response);
          }, 200); // 200ms delay
        })
      );

      const analysisData = {
        transcript: 'Test transcript',
        analyzedEmotions: [{ name: 'joy', score: 0.8 }]
      };

      const result = await AnalysisFactory.generateInsights(analysisData, 'original');

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(result).toBe('Generated insights');
      expect(processingTime).toBeGreaterThan(200); // Should include the delay
      expect(processingTime).toBeLessThan(5000); // But not exceed 5 seconds
    });
  });

  describe('Memory Usage Tests', () => {
    it('should not leak memory when processing multiple responses', () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Process many responses to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const response = {
          results: [{
            results: {
              predictions: [{
                models: {
                  prosody: {
                    groupedPredictions: [{
                      predictions: [{
                        text: `Test sentence ${i}`,
                        emotions: [
                          { name: "joy", score: Math.random() },
                          { name: "sadness", score: Math.random() }
                        ],
                        time: { begin: 0, end: 1 }
                      }]
                    }]
                  }
                }
              }]
            }
          }],
          transcript: `Test sentence ${i}`
        };

        AnalysisFactory.processHumeResponse(response, 'original', 0.3, 5);
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should handle large datasets without excessive memory usage', () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Create a very large response
      const largeEmotions = Array.from({ length: 100 }, (_, i) => ({
        name: `emotion${i}`,
        score: Math.random()
      }));

      const largePredictions = Array.from({ length: 100 }, (_, i) => ({
        text: `This is a longer sentence ${i} with more content to test memory usage`,
        emotions: largeEmotions,
        time: { begin: i, end: i + 1 }
      }));

      const largeResponse = {
        results: [{
          results: {
            predictions: [{
              models: {
                prosody: {
                  groupedPredictions: [{
                    predictions: largePredictions
                  }]
                }
              }
            }]
          }
        }],
        transcript: largePredictions.map(p => p.text).join(' ')
      };

      const result = AnalysisFactory.processHumeResponse(
        largeResponse,
        'sentence-level',
        0.1,
        50
      );

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      expect(result.emotions.length).toBeGreaterThan(0);
      expect(result.sentenceEmotions?.length).toBeGreaterThan(0);
      // Memory increase should be reasonable even for large datasets
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // Less than 100MB
    });
  });

  describe('Concurrent User Simulation', () => {
    it('should handle multiple concurrent analysis requests', async () => {
      const concurrentRequests = 10;
      const startTime = performance.now();

      // Mock fast responses for all requests
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'Concurrent insights' } }]
        })
      } as Response);

      // Create multiple concurrent requests
      const requests = Array.from({ length: concurrentRequests }, (_, i) => {
        const analysisData = {
          transcript: `Concurrent test ${i}`,
          analyzedEmotions: [{ name: 'joy', score: 0.8 }]
        };
        return AnalysisFactory.generateInsights(analysisData, 'original');
      });

      // Wait for all requests to complete
      const results = await Promise.all(requests);

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // All requests should succeed
      expect(results).toHaveLength(concurrentRequests);
      results.forEach(result => {
        expect(result).toBe('Concurrent insights');
      });

      // Total time should be reasonable (not much longer than a single request)
      expect(totalTime).toBeLessThan(2000); // Less than 2 seconds for 10 concurrent requests
    });

    it('should handle concurrent audio recording operations', async () => {
      const concurrentRecorders = 5;
      const startTime = performance.now();

      // Create multiple audio recorders
      const recorders = Array.from({ length: concurrentRecorders }, () => new AudioRecorder());

      // Start all recordings concurrently
      const startPromises = recorders.map(recorder => recorder.startRecording());

      // Wait for all to start
      await Promise.all(startPromises);

      const endTime = performance.now();
      const startupTime = endTime - startTime;

      // All recorders should be in recording state
      recorders.forEach(recorder => {
        expect(recorder.isRecording()).toBe(true);
      });

      // Startup time should be reasonable
      expect(startupTime).toBeLessThan(1000); // Less than 1 second to start 5 recorders
    });

    it('should maintain performance under load', async () => {
      const loadTestIterations = 50;
      const responseTimes: number[] = [];

      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'Load test insights' } }]
        })
      } as Response);

      // Run multiple iterations to simulate load
      for (let i = 0; i < loadTestIterations; i++) {
        const startTime = performance.now();

        const analysisData = {
          transcript: `Load test iteration ${i}`,
          analyzedEmotions: [{ name: 'joy', score: 0.8 }]
        };

        await AnalysisFactory.generateInsights(analysisData, 'original');

        const endTime = performance.now();
        responseTimes.push(endTime - startTime);
      }

      // Calculate performance metrics
      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      const minResponseTime = Math.min(...responseTimes);

      // Performance should be consistent
      expect(averageResponseTime).toBeLessThan(100); // Average under 100ms
      expect(maxResponseTime).toBeLessThan(500); // Max under 500ms
      expect(maxResponseTime - minResponseTime).toBeLessThan(400); // Variance under 400ms
    });
  });

  describe('Scalability Tests', () => {
    it('should scale linearly with input size', () => {
      const testSizes = [10, 50, 100, 200];
      const processingTimes: number[] = [];

      testSizes.forEach(size => {
        const startTime = performance.now();

        const emotions = Array.from({ length: size }, (_, i) => ({
          name: `emotion${i}`,
          score: Math.random()
        }));

        const predictions = Array.from({ length: size }, (_, i) => ({
          text: `Sentence ${i}`,
          emotions: emotions.slice(0, 10), // Limit emotions per prediction
          time: { begin: i, end: i + 1 }
        }));

        const response = {
          results: [{
            results: {
              predictions: [{
                models: {
                  prosody: {
                    groupedPredictions: [{
                      predictions: predictions
                    }]
                  }
                }
              }]
            }
          }],
          transcript: predictions.map(p => p.text).join(' ')
        };

        AnalysisFactory.processHumeResponse(response, 'original', 0.3, 10);

        const endTime = performance.now();
        processingTimes.push(endTime - startTime);
      });

      // Processing time should scale reasonably with input size
      // Later sizes should not be exponentially slower
      const firstTime = processingTimes[0];
      const lastTime = processingTimes[processingTimes.length - 1];
      const scalingFactor = lastTime / firstTime;

      // Should not scale worse than O(n^2)
      const inputScalingFactor = testSizes[testSizes.length - 1] / testSizes[0];
      expect(scalingFactor).toBeLessThan(inputScalingFactor * inputScalingFactor);
    });
  });
});
