import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

export type UserState =
  | { status: "ANONYMOUS" }
  | { status: "GUEST_PASS"; accessCode: string }
  | { status: "AUTHENTICATED_NO_CREDITS"; user: any; profile: any }
  | {
      status: "AUTHENTICATED_WITH_FREE_USAGES";
      user: any;
      profile: any;
      freeUsagesRemaining: number;
    }
  | { status: "AUTHENTICATED_WITH_CREDITS"; user: any; profile: any };

export async function getUserState(accessCode?: string): Promise<UserState> {
  // Create Supabase client for server-side operations
  const cookieStore = await cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set() {
          // No-op for server-side
        },
        remove() {
          // No-op for server-side
        },
      },
    }
  );

  // Check for user session
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (session?.user) {
    // User is authenticated, check their subscription status
    const { data: profile } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", session.user.id)
      .single();

    if (profile) {
      // Check if user has active subscription
      const hasActiveSubscription =
        profile.subscription_status === "active" &&
        (!profile.current_period_end ||
          new Date(profile.current_period_end) > new Date());

      if (hasActiveSubscription) {
        return {
          status: "AUTHENTICATED_WITH_CREDITS",
          user: session.user,
          profile,
        };
      } else if (profile.free_usages_remaining > 0) {
        return {
          status: "AUTHENTICATED_WITH_FREE_USAGES",
          user: session.user,
          profile,
          freeUsagesRemaining: profile.free_usages_remaining,
        };
      } else {
        return {
          status: "AUTHENTICATED_NO_CREDITS",
          user: session.user,
          profile,
        };
      }
    } else {
      // Profile doesn't exist, treat as having free usages
      return {
        status: "AUTHENTICATED_WITH_FREE_USAGES",
        user: session.user,
        profile: null,
        freeUsagesRemaining: 10,
      };
    }
  }

  // No session, check for access code
  if (accessCode) {
    const isValid = await validateAccessCode(accessCode);
    if (isValid) {
      return {
        status: "GUEST_PASS",
        accessCode,
      };
    }
  }

  // No session and no valid access code
  return { status: "ANONYMOUS" };
}

async function validateAccessCode(code: string): Promise<boolean> {
  try {
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get() {
            return undefined;
          },
          set() {},
          remove() {},
        },
      }
    );

    const { data: accessCode, error } = await supabase
      .from("access_codes")
      .select("*")
      .eq("code", code)
      .single();

    if (error || !accessCode) {
      return false;
    }

    // Check if code is expired
    if (accessCode.expires_at && new Date(accessCode.expires_at) < new Date()) {
      return false;
    }

    // Check if code has reached max uses
    if (accessCode.current_uses >= accessCode.max_uses) {
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error validating access code:", error);
    return false;
  }
}

// Helper function to check if user can perform analysis
export function canPerformAnalysis(userState: UserState): boolean {
  return (
    userState.status === "GUEST_PASS" ||
    userState.status === "AUTHENTICATED_WITH_FREE_USAGES" ||
    userState.status === "AUTHENTICATED_WITH_CREDITS"
  );
}

// Helper function to get display name for user state
export function getUserStateDisplayName(userState: UserState): string {
  switch (userState.status) {
    case "ANONYMOUS":
      return "Anonymous";
    case "GUEST_PASS":
      return "Guest";
    case "AUTHENTICATED_NO_CREDITS":
      return "User (No Subscription)";
    case "AUTHENTICATED_WITH_CREDITS":
      return "Subscriber";
  }
}
