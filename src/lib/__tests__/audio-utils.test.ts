import { AudioRecorder, convertBlobToBase64, formatDuration } from '../audio-utils';

// Mock MediaRecorder
const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  state: 'inactive',
  ondataavailable: null,
  onstop: null,
};

// Mock MediaStream
const mockMediaStream = {
  getTracks: jest.fn(() => [
    { stop: jest.fn() },
    { stop: jest.fn() }
  ]),
};

// Mock navigator.mediaDevices.getUserMedia
const mockGetUserMedia = jest.fn();

beforeEach(() => {
  jest.clearAllMocks();
  
  // Reset MediaRecorder mock
  global.MediaRecorder = jest.fn(() => mockMediaRecorder) as any;
  
  // Reset getUserMedia mock
  Object.defineProperty(navigator, 'mediaDevices', {
    writable: true,
    value: {
      getUserMedia: mockGetUserMedia,
    },
  });
  
  // Reset FileReader mock
  global.FileReader = jest.fn(() => ({
    readAsDataURL: jest.fn(),
    onload: null,
    onerror: null,
    result: null,
  })) as any;
});

describe('AudioRecorder', () => {
  let audioRecorder: AudioRecorder;

  beforeEach(() => {
    audioRecorder = new AudioRecorder();
  });

  describe('startRecording', () => {
    it('should start recording successfully', async () => {
      mockGetUserMedia.mockResolvedValue(mockMediaStream);
      
      await audioRecorder.startRecording();
      
      expect(mockGetUserMedia).toHaveBeenCalledWith({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        }
      });
      expect(MediaRecorder).toHaveBeenCalledWith(mockMediaStream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      expect(mockMediaRecorder.start).toHaveBeenCalled();
    });

    it('should throw error when getUserMedia fails', async () => {
      const error = new Error('Permission denied');
      mockGetUserMedia.mockRejectedValue(error);
      
      await expect(audioRecorder.startRecording()).rejects.toThrow(
        'Failed to start recording. Please check microphone permissions.'
      );
    });
  });

  describe('stopRecording', () => {
    it('should stop recording and return audio blob', async () => {
      mockGetUserMedia.mockResolvedValue(mockMediaStream);
      await audioRecorder.startRecording();
      
      const mockBlob = new Blob(['audio data'], { type: 'audio/webm' });
      
      // Simulate the stop event
      const stopPromise = audioRecorder.stopRecording();
      
      // Trigger the onstop callback
      if (mockMediaRecorder.onstop) {
        mockMediaRecorder.onstop();
      }
      
      const result = await stopPromise;
      
      expect(mockMediaRecorder.stop).toHaveBeenCalled();
      expect(result).toBeInstanceOf(Blob);
    });

    it('should reject if no recording in progress', async () => {
      await expect(audioRecorder.stopRecording()).rejects.toThrow(
        'No recording in progress'
      );
    });
  });

  describe('isRecording', () => {
    it('should return true when recording', async () => {
      mockGetUserMedia.mockResolvedValue(mockMediaStream);
      mockMediaRecorder.state = 'recording';
      
      await audioRecorder.startRecording();
      
      expect(audioRecorder.isRecording()).toBe(true);
    });

    it('should return false when not recording', () => {
      expect(audioRecorder.isRecording()).toBe(false);
    });
  });
});

describe('convertBlobToBase64', () => {
  it('should convert blob to base64 string', async () => {
    const mockBlob = new Blob(['test data'], { type: 'audio/webm' });
    const mockFileReader = {
      readAsDataURL: jest.fn(),
      onload: null,
      onerror: null,
      result: 'data:audio/webm;base64,dGVzdCBkYXRh',
    };
    
    global.FileReader = jest.fn(() => mockFileReader) as any;
    
    const promise = convertBlobToBase64(mockBlob);
    
    // Simulate successful read
    if (mockFileReader.onload) {
      mockFileReader.onload();
    }
    
    const result = await promise;
    
    expect(result).toBe('dGVzdCBkYXRh');
    expect(mockFileReader.readAsDataURL).toHaveBeenCalledWith(mockBlob);
  });

  it('should reject on FileReader error', async () => {
    const mockBlob = new Blob(['test data'], { type: 'audio/webm' });
    const mockError = new Error('FileReader error');
    const mockFileReader = {
      readAsDataURL: jest.fn(),
      onload: null,
      onerror: null,
      result: null,
    };
    
    global.FileReader = jest.fn(() => mockFileReader) as any;
    
    const promise = convertBlobToBase64(mockBlob);
    
    // Simulate error
    if (mockFileReader.onerror) {
      mockFileReader.onerror(mockError);
    }
    
    await expect(promise).rejects.toBe(mockError);
  });
});

describe('formatDuration', () => {
  it('should format seconds correctly', () => {
    expect(formatDuration(0)).toBe('0:00');
    expect(formatDuration(30)).toBe('0:30');
    expect(formatDuration(60)).toBe('1:00');
    expect(formatDuration(90)).toBe('1:30');
    expect(formatDuration(3661)).toBe('61:01');
  });

  it('should handle decimal seconds', () => {
    expect(formatDuration(30.7)).toBe('0:30');
    expect(formatDuration(90.9)).toBe('1:30');
  });
});
