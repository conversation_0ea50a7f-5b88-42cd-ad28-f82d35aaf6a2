import { AnalysisFactory } from "../analysis-factory";
import { AnalysisType } from "@/types/analysis";

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe("AnalysisFactory", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("processHumeResponse", () => {
    const mockHumeResponse = {
      results: [
        {
          results: {
            predictions: [
              {
                models: {
                  prosody: {
                    groupedPredictions: [
                      {
                        predictions: [
                          {
                            text: "Hello world",
                            emotions: [
                              { name: "joy", score: 0.8 },
                              { name: "sadness", score: 0.2 },
                              { name: "anger", score: 0.1 },
                            ],
                            time: { begin: 0, end: 2 },
                          },
                        ],
                      },
                    ],
                  },
                  burst: {
                    groupedPredictions: [
                      {
                        predictions: [
                          {
                            emotions: [
                              { name: "joy", score: 0.7 },
                              { name: "excitement", score: 0.6 },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
      ],
      transcript: "Hello world",
    };

    it("should process standard response correctly", () => {
      const result = AnalysisFactory.processHumeResponse(
        mockHumeResponse,
        "original",
        0.3,
        5
      );

      expect(result.transcript).toBe("Hello world");
      expect(result.emotions).toHaveLength(4); // joy, sadness, anger, excitement
      expect(result.emotions[0].name).toBe("joy");
      expect(result.emotions[0].score).toBe(0.8); // Max of prosody and burst
      expect(result.analyzedEmotions).toHaveLength(2); // joy (0.8) and excitement (0.6) above threshold 0.3
      expect(result.sentenceEmotions).toBeUndefined();
    });

    it("should process sentence-level response correctly", () => {
      const result = AnalysisFactory.processHumeResponse(
        mockHumeResponse,
        "sentence-level",
        0.3,
        5
      );

      expect(result.transcript).toBe("Hello world");
      expect(result.emotions).toHaveLength(3); // Aggregated emotions
      expect(result.analyzedEmotions).toHaveLength(1); // Only joy (0.8) above threshold 0.3
      expect(result.sentenceEmotions).toHaveLength(1);
      expect(result.sentenceEmotions![0].sentence).toBe("Hello world");
      expect(result.sentenceEmotions![0].emotions).toHaveLength(3);
    });

    it("should handle empty response", () => {
      const emptyResponse = { results: [] };

      const result = AnalysisFactory.processHumeResponse(
        emptyResponse,
        "original",
        0.3,
        5
      );

      expect(result.transcript).toBe("");
      expect(result.emotions).toHaveLength(0);
      expect(result.analyzedEmotions).toHaveLength(0);
    });

    it("should filter emotions by threshold", () => {
      const result = AnalysisFactory.processHumeResponse(
        mockHumeResponse,
        "original",
        0.5, // Higher threshold
        5
      );

      expect(result.analyzedEmotions).toHaveLength(2); // Only joy (0.8) and excitement (0.6)
    });

    it("should limit emotions by maxEmotions", () => {
      const result = AnalysisFactory.processHumeResponse(
        mockHumeResponse,
        "original",
        0.1, // Low threshold
        2 // Max 2 emotions
      );

      expect(result.analyzedEmotions).toHaveLength(2);
    });
  });

  describe("generateInsights", () => {
    it("should generate insights successfully", async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ insights: "Generated insights" }),
      };
      mockFetch.mockResolvedValue(mockResponse);

      const analysisData = {
        transcript: "Hello world",
        analyzedEmotions: [{ name: "joy", score: 0.8 }],
      };

      const result = await AnalysisFactory.generateInsights(
        analysisData,
        "original"
      );

      expect(result).toBe("Generated insights");
      expect(mockFetch).toHaveBeenCalledWith("/api/generate-insights", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          transcript: "Hello world",
          topEmotions: [{ name: "joy", score: 0.8 }],
          analysisType: "original",
          sentenceEmotions: undefined,
        }),
      });
    });

    it("should throw error when API call fails", async () => {
      const mockResponse = {
        ok: false,
        status: 500,
      };
      mockFetch.mockResolvedValue(mockResponse);

      const analysisData = {
        transcript: "Hello world",
        analyzedEmotions: [{ name: "joy", score: 0.8 }],
      };

      await expect(
        AnalysisFactory.generateInsights(analysisData, "original")
      ).rejects.toThrow("Failed to generate insights");
    });

    it("should include sentence emotions for sentence-level analysis", async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ insights: "Generated insights" }),
      };
      mockFetch.mockResolvedValue(mockResponse);

      const analysisData = {
        transcript: "Hello world",
        analyzedEmotions: [{ name: "joy", score: 0.8 }],
        sentenceEmotions: [
          {
            sentence: "Hello world",
            emotions: [{ name: "joy", score: 0.8 }],
          },
        ],
      };

      await AnalysisFactory.generateInsights(analysisData, "sentence-level");

      const callArgs = mockFetch.mock.calls[0][1];
      const body = JSON.parse(callArgs.body);

      expect(body.sentenceEmotions).toBeDefined();
      expect(body.sentenceEmotions).toHaveLength(1);
    });
  });

  describe("createAnalysisData", () => {
    it("should create complete analysis data object", () => {
      const processedResponse = {
        emotions: [{ name: "joy", score: 0.8 }],
        analyzedEmotions: [{ name: "joy", score: 0.8 }],
        transcript: "Hello world",
      };

      const result = AnalysisFactory.createAnalysisData(
        processedResponse,
        "Generated insights",
        "original"
      );

      expect(result).toEqual({
        transcript: "Hello world",
        emotions: [{ name: "joy", score: 0.8 }],
        analyzedEmotions: [{ name: "joy", score: 0.8 }],
        sentenceEmotions: undefined,
        insights: "Generated insights",
        analysisType: "original",
      });
    });

    it("should include sentence emotions when provided", () => {
      const processedResponse = {
        emotions: [{ name: "joy", score: 0.8 }],
        analyzedEmotions: [{ name: "joy", score: 0.8 }],
        sentenceEmotions: [
          {
            sentence: "Hello world",
            emotions: [{ name: "joy", score: 0.8 }],
          },
        ],
        transcript: "Hello world",
      };

      const result = AnalysisFactory.createAnalysisData(
        processedResponse,
        "Generated insights",
        "sentence-level"
      );

      expect(result.sentenceEmotions).toBeDefined();
      expect(result.sentenceEmotions).toHaveLength(1);
    });
  });
});
