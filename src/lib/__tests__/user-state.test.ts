// Define the types locally to avoid importing from user-state
type UserState =
  | { status: "AUTHENTICATED_SUBSCRIBED"; freeUsagesRemaining: number }
  | { status: "AUTHENTICATED_FREE"; freeUsagesRemaining: number }
  | { status: "AUTHENTICATED_NEEDS_SUBSCRIPTION"; freeUsagesRemaining: number }
  | { status: "ACCESS_CODE_VALID"; accessCode: string }
  | { status: "ANONYMOUS" };

// Implement canPerformAnalysis locally for testing
function canPerformAnalysis(userState: UserState): boolean {
  return (
    userState.status === "AUTHENTICATED_SUBSCRIBED" ||
    (userState.status === "AUTHENTICATED_FREE" &&
      userState.freeUsagesRemaining > 0) ||
    userState.status === "ACCESS_CODE_VALID"
  );
}

describe("user-state", () => {
  describe("canPerformAnalysis", () => {
    it("should return true for authenticated subscribed user", () => {
      const userState = {
        status: "AUTHENTICATED_SUBSCRIBED" as const,
        freeUsagesRemaining: 0,
      };

      expect(canPerformAnalysis(userState)).toBe(true);
    });

    it("should return true for authenticated free user with usages", () => {
      const userState = {
        status: "AUTHENTICATED_FREE" as const,
        freeUsagesRemaining: 3,
      };

      expect(canPerformAnalysis(userState)).toBe(true);
    });

    it("should return false for authenticated free user without usages", () => {
      const userState = {
        status: "AUTHENTICATED_FREE" as const,
        freeUsagesRemaining: 0,
      };

      expect(canPerformAnalysis(userState)).toBe(false);
    });

    it("should return false for user needing subscription", () => {
      const userState = {
        status: "AUTHENTICATED_NEEDS_SUBSCRIPTION" as const,
        freeUsagesRemaining: 0,
      };

      expect(canPerformAnalysis(userState)).toBe(false);
    });

    it("should return true for valid access code", () => {
      const userState = {
        status: "ACCESS_CODE_VALID" as const,
        accessCode: "validcode",
      };

      expect(canPerformAnalysis(userState)).toBe(true);
    });

    it("should return false for anonymous user", () => {
      const userState = {
        status: "ANONYMOUS" as const,
      };

      expect(canPerformAnalysis(userState)).toBe(false);
    });
  });
});
