'use client';

import { useState } from 'react';
import VoiceRecorder from '@/components/VoiceRecorder';
import ResultsPage from '@/components/ResultsPage';
import { AnalysisSettings } from '@/types/analysis';

interface AuthenticatedWithCreditsViewProps {
  user: any;
  profile: any;
}

export default function AuthenticatedWithCreditsView({ user, profile }: AuthenticatedWithCreditsViewProps) {
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [analysisSettings, setAnalysisSettings] = useState<AnalysisSettings | null>(null);

  const handleRecordingComplete = (blob: Blob, settings: AnalysisSettings) => {
    setAudioBlob(blob);
    setAnalysisSettings(settings);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Subscriber Status Banner */}
      <div className="bg-gradient-to-r from-emerald-50/50 to-green-50/50 border-b border-emerald-100/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-emerald-500 text-lg">✨</span>
              <span className="text-sm text-emerald-700 font-light">
                Welcome back, {user.email}! You have unlimited analysis access.
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-xs text-emerald-600 bg-emerald-100/70 px-3 py-1 rounded-full font-medium">
                Alpha Subscriber
              </span>
              {profile?.isAlphaUser && (
                <span className="text-xs text-blue-600 bg-blue-100/70 px-3 py-1 rounded-full font-medium">
                  Alpha Pricing Locked
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {!audioBlob ? (
          <VoiceRecorder
            onRecordingComplete={handleRecordingComplete}
            isProcessing={false}
          />
        ) : (
          <ResultsPage audioBlob={audioBlob} settings={analysisSettings!} />
        )}
      </div>
    </div>
  );
}
