'use client';

import { useState } from 'react';
import AuthModal from '../AuthModal';

export default function AnonymousView() {
  const [accessCode, setAccessCode] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const handleAccessCodeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (accessCode.trim()) {
      // Redirect with access code
      window.location.href = `/?accessCode=${encodeURIComponent(accessCode.trim())}`;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Hero Section */}
          <div className="mb-12">
            <h1 className="text-5xl font-light text-gray-800 mb-6">
              🎤 Voice Emotion Analysis
            </h1>
            <p className="text-xl text-gray-600 mb-8 font-light leading-relaxed">
              Hey there! Let's explore the emotions in your voice together ✨
            </p>
          </div>

          {/* Access Options */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-sm border border-orange-100/30 p-8 mb-8">
            <h2 className="text-2xl font-light text-gray-800 mb-8 text-center">
              Let's get you started! 🌟
            </h2>

            {/* Access Code Form */}
            <form onSubmit={handleAccessCodeSubmit} className="mb-8">
              <div className="flex flex-col sm:flex-row gap-3">
                <input
                  type="text"
                  value={accessCode}
                  onChange={(e) => setAccessCode(e.target.value)}
                  placeholder="Got an access code? Pop it in here!"
                  className="flex-1 px-5 py-4 border border-orange-200/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-orange-300/30 focus:border-orange-300 transition-all bg-white/50 text-gray-700 placeholder-gray-400"
                />
                <button
                  type="submit"
                  disabled={!accessCode.trim()}
                  className="px-8 py-4 bg-gradient-to-r from-orange-400 to-amber-500 text-white rounded-2xl hover:from-orange-500 hover:to-amber-600 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                >
                  Let's go!
                </button>
              </div>
            </form>

            <div className="relative mb-8">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-orange-200/50" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-4 bg-white/70 text-gray-400 font-light">or</span>
              </div>
            </div>

            {/* Sign Up Button */}
            <button
              onClick={() => setIsAuthModalOpen(true)}
              className="w-full px-8 py-4 bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white rounded-2xl hover:from-orange-500 hover:via-red-500 hover:to-pink-500 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg font-medium"
            >
              Create account & get 10 free tries! 🎉
            </button>

            <p className="text-sm text-gray-400 mt-4 text-center font-light">
              Then just $20/month for unlimited • Alpha pricing • Cancel anytime
            </p>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-orange-100/30 hover:bg-white/80 transition-all duration-300">
              <div className="text-3xl mb-4 opacity-80">🎯</div>
              <h3 className="font-medium text-gray-800 mb-3">Spot Your Emotions</h3>
              <p className="text-gray-500 text-sm leading-relaxed font-light">
                Our AI picks up on the subtle feelings in your voice that you might miss
              </p>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-orange-100/30 hover:bg-white/80 transition-all duration-300">
              <div className="text-3xl mb-4 opacity-80">💡</div>
              <h3 className="font-medium text-gray-800 mb-3">Get Real Insights</h3>
              <p className="text-gray-500 text-sm leading-relaxed font-light">
                Understand what's really going on emotionally and discover patterns
              </p>
            </div>

            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-orange-100/30 hover:bg-white/80 transition-all duration-300">
              <div className="text-3xl mb-4 opacity-80">🤗</div>
              <h3 className="font-medium text-gray-800 mb-3">Totally Private</h3>
              <p className="text-gray-500 text-sm leading-relaxed font-light">
                Your voice stays safe with us - we analyze it but don't keep recordings
              </p>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode="sign_up"
      />
    </div>
  );
}
