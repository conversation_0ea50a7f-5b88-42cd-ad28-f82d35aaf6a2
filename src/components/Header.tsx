'use client';

import { useSupabase } from './SupabaseProvider';
import Link from 'next/link';
import { useState } from 'react';
import AuthModal from './AuthModal';

export default function Header() {
  const { user, supabase, loading } = useSupabase();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'sign_in' | 'sign_up'>('sign_in');

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    setIsMenuOpen(false);
  };

  if (loading) {
    return (
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="text-xl font-bold text-gray-900">
              Voice Emotion Analysis
            </Link>
            <div className="animate-pulse bg-gray-200 h-8 w-20 rounded"></div>
          </div>
        </div>
      </header>
    );
  }

  return (
    <header className="bg-white/80 backdrop-blur-md border-b border-gray-100/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="text-xl font-light text-gray-800 hover:text-gray-900 transition-colors">
            Voice Emotion Analysis
          </Link>

          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-500 hover:text-gray-700 transition-colors font-light">
              Home
            </Link>

            {user ? (
              <>
                <Link href="/history" className="text-gray-500 hover:text-gray-700 transition-colors font-light">
                  History
                </Link>
                <Link href="/account" className="text-gray-500 hover:text-gray-700 transition-colors font-light">
                  Account
                </Link>
                <div className="relative">
                  <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    className="flex items-center space-x-3 text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-amber-500 rounded-full flex items-center justify-center text-white text-sm font-medium shadow-sm">
                      {user.email?.charAt(0).toUpperCase()}
                    </div>
                    <span className="text-sm font-light">{user.email}</span>
                  </button>

                  {isMenuOpen && (
                    <div className="absolute right-0 mt-3 w-48 bg-white/95 backdrop-blur-md rounded-2xl shadow-lg border border-gray-100/50 py-2 z-50">
                      <button
                        onClick={handleSignOut}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-600 hover:bg-gray-50/50 hover:text-gray-800 transition-colors font-light rounded-xl mx-1"
                      >
                        Sign Out
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => {
                    setAuthMode('sign_in');
                    setIsAuthModalOpen(true);
                  }}
                  className="text-gray-500 hover:text-gray-700 transition-colors font-light"
                >
                  Sign In
                </button>
                <button
                  onClick={() => {
                    setAuthMode('sign_up');
                    setIsAuthModalOpen(true);
                  }}
                  className="bg-gradient-to-r from-orange-400 to-amber-500 text-white px-6 py-2 rounded-2xl hover:from-orange-500 hover:to-amber-600 transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                >
                  Get 10 Free Tries
                </button>
              </div>
            )}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-600 hover:text-gray-900"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <Link href="/" className="block px-3 py-2 text-gray-600 hover:text-gray-900">
                Home
              </Link>

              {user ? (
                <>
                  <Link href="/history" className="block px-3 py-2 text-gray-600 hover:text-gray-900">
                    History
                  </Link>
                  <Link href="/account" className="block px-3 py-2 text-gray-600 hover:text-gray-900">
                    Account
                  </Link>
                  <div className="px-3 py-2 text-sm text-gray-500">
                    Signed in as {user.email}
                  </div>
                  <button
                    onClick={handleSignOut}
                    className="block w-full text-left px-3 py-2 text-gray-600 hover:text-gray-900"
                  >
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={() => {
                      setAuthMode('sign_in');
                      setIsAuthModalOpen(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left px-3 py-2 text-gray-600 hover:text-gray-900"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => {
                      setAuthMode('sign_up');
                      setIsAuthModalOpen(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full text-left px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Sign Up
                  </button>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode={authMode}
      />
    </header>
  );
}
