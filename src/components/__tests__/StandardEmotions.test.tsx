import { render, screen } from '@testing-library/react';
import StandardEmotions from '../StandardEmotions';
import { EmotionScore } from '@/types/analysis';

describe('StandardEmotions', () => {
  const mockEmotions: EmotionScore[] = [
    { name: 'joy', score: 0.8 },
    { name: 'sadness', score: 0.6 },
    { name: 'anger', score: 0.4 },
    { name: 'fear', score: 0.2 },
  ];

  const mockAnalyzedEmotions: EmotionScore[] = [
    { name: 'joy', score: 0.8 },
    { name: 'sadness', score: 0.6 },
  ];

  it('renders emotions with correct scores', () => {
    render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={mockAnalyzedEmotions}
      />
    );

    expect(screen.getByText('joy')).toBeInTheDocument();
    expect(screen.getByText('80.0%')).toBeInTheDocument();
    expect(screen.getByText('sadness')).toBeInTheDocument();
    expect(screen.getByText('60.0%')).toBeInTheDocument();
    expect(screen.getByText('anger')).toBeInTheDocument();
    expect(screen.getByText('40.0%')).toBeInTheDocument();
    expect(screen.getByText('fear')).toBeInTheDocument();
    expect(screen.getByText('20.0%')).toBeInTheDocument();
  });

  it('shows AI analyzed badges for analyzed emotions', () => {
    render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={mockAnalyzedEmotions}
      />
    );

    const aiAnalyzedBadges = screen.getAllByText('AI Analyzed');
    expect(aiAnalyzedBadges).toHaveLength(2); // joy and sadness
  });

  it('displays correct count of analyzed emotions in header', () => {
    render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={mockAnalyzedEmotions}
      />
    );

    expect(screen.getByText('(Top 2 analyzed by AI)')).toBeInTheDocument();
  });

  it('shows summary of analyzed emotions at bottom', () => {
    render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={mockAnalyzedEmotions}
      />
    );

    expect(screen.getByText('2 emotions analyzed by AI')).toBeInTheDocument();
  });

  it('handles singular emotion count correctly', () => {
    const singleAnalyzedEmotion = [{ name: 'joy', score: 0.8 }];
    
    render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={singleAnalyzedEmotion}
      />
    );

    expect(screen.getByText('(Top 1 analyzed by AI)')).toBeInTheDocument();
    expect(screen.getByText('1 emotion analyzed by AI')).toBeInTheDocument();
  });

  it('limits display to 10 emotions', () => {
    const manyEmotions: EmotionScore[] = Array.from({ length: 15 }, (_, i) => ({
      name: `emotion${i}`,
      score: 0.1 * (15 - i),
    }));

    render(
      <StandardEmotions
        emotions={manyEmotions}
        analyzedEmotions={[]}
      />
    );

    // Should only show first 10 emotions
    expect(screen.getByText('emotion0')).toBeInTheDocument();
    expect(screen.getByText('emotion9')).toBeInTheDocument();
    expect(screen.queryByText('emotion10')).not.toBeInTheDocument();
  });

  it('shows empty state when no emotions provided', () => {
    render(
      <StandardEmotions
        emotions={[]}
        analyzedEmotions={[]}
      />
    );

    expect(screen.getByText('No emotion data available.')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={mockAnalyzedEmotions}
        className="custom-class"
      />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('renders emotion numbers correctly', () => {
    render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={mockAnalyzedEmotions}
      />
    );

    expect(screen.getByText('1')).toBeInTheDocument(); // joy
    expect(screen.getByText('2')).toBeInTheDocument(); // sadness
    expect(screen.getByText('3')).toBeInTheDocument(); // anger
    expect(screen.getByText('4')).toBeInTheDocument(); // fear
  });

  it('does not show analyzed emotions summary when no emotions are analyzed', () => {
    render(
      <StandardEmotions
        emotions={mockEmotions}
        analyzedEmotions={[]}
      />
    );

    expect(screen.queryByText(/emotions analyzed by AI/)).not.toBeInTheDocument();
  });

  it('capitalizes emotion names', () => {
    const emotionsWithLowercase: EmotionScore[] = [
      { name: 'joy', score: 0.8 },
      { name: 'contentment', score: 0.6 },
    ];

    render(
      <StandardEmotions
        emotions={emotionsWithLowercase}
        analyzedEmotions={[]}
      />
    );

    // Check that the emotion names are displayed (they should be capitalized via CSS)
    expect(screen.getByText('joy')).toBeInTheDocument();
    expect(screen.getByText('contentment')).toBeInTheDocument();
  });
});
