import { render, screen } from '@testing-library/react';
import EmotionDisplay from '../EmotionDisplay';
import { AnalysisData } from '@/types/analysis';

// Mock the child components to focus on EmotionDisplay logic
jest.mock('../StandardEmotions', () => {
  return function MockStandardEmotions({ emotions, analyzedEmotions, className }: any) {
    return (
      <div data-testid="standard-emotions" className={className}>
        <div>Standard Emotions Component</div>
        <div>Emotions: {emotions.length}</div>
        <div>Analyzed: {analyzedEmotions.length}</div>
      </div>
    );
  };
});

jest.mock('../SentenceLevelEmotions', () => {
  return function MockSentenceLevelEmotions({ sentenceEmotions }: any) {
    return (
      <div data-testid="sentence-level-emotions">
        <div>Sentence Level Emotions Component</div>
        <div>Sentences: {sentenceEmotions.length}</div>
      </div>
    );
  };
});

describe('EmotionDisplay', () => {
  const baseAnalysisData: AnalysisData = {
    transcript: 'Test transcript',
    emotions: [
      { name: 'joy', score: 0.8 },
      { name: 'sadness', score: 0.6 },
    ],
    analyzedEmotions: [
      { name: 'joy', score: 0.8 },
    ],
    insights: 'Test insights',
    analysisType: 'original',
  };

  it('renders StandardEmotions for original analysis type', () => {
    render(<EmotionDisplay analysisData={baseAnalysisData} />);

    expect(screen.getByTestId('standard-emotions')).toBeInTheDocument();
    expect(screen.getByText('Standard Emotions Component')).toBeInTheDocument();
    expect(screen.queryByTestId('sentence-level-emotions')).not.toBeInTheDocument();
  });

  it('renders StandardEmotions for actions analysis type', () => {
    const actionsAnalysisData: AnalysisData = {
      ...baseAnalysisData,
      analysisType: 'actions',
    };

    render(<EmotionDisplay analysisData={actionsAnalysisData} />);

    expect(screen.getByTestId('standard-emotions')).toBeInTheDocument();
    expect(screen.queryByTestId('sentence-level-emotions')).not.toBeInTheDocument();
  });

  it('renders SentenceLevelEmotions for sentence-level analysis with sentence data', () => {
    const sentenceLevelAnalysisData: AnalysisData = {
      ...baseAnalysisData,
      analysisType: 'sentence-level',
      sentenceEmotions: [
        {
          sentence: 'First sentence.',
          emotions: [{ name: 'joy', score: 0.8 }],
        },
        {
          sentence: 'Second sentence.',
          emotions: [{ name: 'sadness', score: 0.6 }],
        },
      ],
    };

    render(<EmotionDisplay analysisData={sentenceLevelAnalysisData} />);

    expect(screen.getByTestId('sentence-level-emotions')).toBeInTheDocument();
    expect(screen.getByText('Sentence Level Emotions Component')).toBeInTheDocument();
    expect(screen.getByText('Sentences: 2')).toBeInTheDocument();
  });

  it('shows overall emotion summary for sentence-level analysis', () => {
    const sentenceLevelAnalysisData: AnalysisData = {
      ...baseAnalysisData,
      analysisType: 'sentence-level',
      sentenceEmotions: [
        {
          sentence: 'Test sentence.',
          emotions: [{ name: 'joy', score: 0.8 }],
        },
      ],
    };

    render(<EmotionDisplay analysisData={sentenceLevelAnalysisData} />);

    expect(screen.getByText('Overall Emotion Summary')).toBeInTheDocument();
    expect(screen.getByText('These are the aggregated emotions across all sentences:')).toBeInTheDocument();
  });

  it('falls back to StandardEmotions when sentence-level has no sentence data', () => {
    const sentenceLevelWithoutSentences: AnalysisData = {
      ...baseAnalysisData,
      analysisType: 'sentence-level',
      sentenceEmotions: [],
    };

    render(<EmotionDisplay analysisData={sentenceLevelWithoutSentences} />);

    expect(screen.getByTestId('standard-emotions')).toBeInTheDocument();
    expect(screen.queryByTestId('sentence-level-emotions')).not.toBeInTheDocument();
  });

  it('uses displayType over analysisType when provided', () => {
    const analysisDataWithDisplayType: AnalysisData = {
      ...baseAnalysisData,
      analysisType: 'original',
      displayType: 'sentence-level',
      sentenceEmotions: [
        {
          sentence: 'Test sentence.',
          emotions: [{ name: 'joy', score: 0.8 }],
        },
      ],
    };

    render(<EmotionDisplay analysisData={analysisDataWithDisplayType} />);

    expect(screen.getByTestId('sentence-level-emotions')).toBeInTheDocument();
    expect(screen.queryByTestId('standard-emotions')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <EmotionDisplay analysisData={baseAnalysisData} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('limits overall emotions to 6 in sentence-level view', () => {
    const manyEmotions = Array.from({ length: 10 }, (_, i) => ({
      name: `emotion${i}`,
      score: 0.1 * (10 - i),
    }));

    const sentenceLevelAnalysisData: AnalysisData = {
      ...baseAnalysisData,
      analysisType: 'sentence-level',
      emotions: manyEmotions,
      sentenceEmotions: [
        {
          sentence: 'Test sentence.',
          emotions: [{ name: 'joy', score: 0.8 }],
        },
      ],
    };

    render(<EmotionDisplay analysisData={sentenceLevelAnalysisData} />);

    // Should show first 6 emotions in the overall summary
    expect(screen.getByText('emotion0')).toBeInTheDocument();
    expect(screen.getByText('emotion5')).toBeInTheDocument();
    expect(screen.queryByText('emotion6')).not.toBeInTheDocument();
  });

  it('highlights analyzed emotions in overall summary', () => {
    const sentenceLevelAnalysisData: AnalysisData = {
      ...baseAnalysisData,
      analysisType: 'sentence-level',
      emotions: [
        { name: 'joy', score: 0.8 },
        { name: 'sadness', score: 0.6 },
      ],
      analyzedEmotions: [
        { name: 'joy', score: 0.8 },
      ],
      sentenceEmotions: [
        {
          sentence: 'Test sentence.',
          emotions: [{ name: 'joy', score: 0.8 }],
        },
      ],
    };

    render(<EmotionDisplay analysisData={sentenceLevelAnalysisData} />);

    expect(screen.getByText('joy')).toBeInTheDocument();
    expect(screen.getByText('80.0%')).toBeInTheDocument();
    expect(screen.getByText('sadness')).toBeInTheDocument();
    expect(screen.getByText('60.0%')).toBeInTheDocument();
  });
});
