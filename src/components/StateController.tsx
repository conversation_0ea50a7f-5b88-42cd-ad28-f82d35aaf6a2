import { type UserState } from "@/lib/user-state";
import AnonymousView from "@/components/views/AnonymousView";
import GuestPassView from "@/components/views/GuestPassView";
import AuthenticatedNoCreditsView from "@/components/views/AuthenticatedNoCreditsView";
import AuthenticatedWithFreeUsagesView from "@/components/views/AuthenticatedWithFreeUsagesView";
import AuthenticatedWithCreditsView from "@/components/views/AuthenticatedWithCreditsView";

interface StateControllerProps {
  userState: UserState;
}

export default function StateController({ userState }: StateControllerProps) {
  switch (userState.status) {
    case 'ANONYMOUS':
      return <AnonymousView />;

    case 'GUEST_PASS':
      return <GuestPassView accessCode={userState.accessCode} />;

    case 'AUTHENTICATED_NO_CREDITS':
      return <AuthenticatedNoCreditsView user={userState.user} profile={userState.profile} />;

    case 'AUTHENTICATED_WITH_FREE_USAGES':
      return <AuthenticatedWithFreeUsagesView
        user={userState.user}
        profile={userState.profile}
        freeUsagesRemaining={userState.freeUsagesRemaining}
      />;

    case 'AUTHENTICATED_WITH_CREDITS':
      return <AuthenticatedWithCreditsView user={userState.user} profile={userState.profile} />;

    default:
      return <AnonymousView />;
  }
}
