'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from './SupabaseProvider';
import { Auth } from '@supabase/auth-ui-react';
import { ThemeSupa } from '@supabase/auth-ui-shared';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialMode?: 'sign_in' | 'sign_up';
}

export default function AuthModal({ isOpen, onClose, initialMode = 'sign_in' }: AuthModalProps) {
  const { supabase, session } = useSupabase();
  const [mode, setMode] = useState<'sign_in' | 'sign_up'>(initialMode);

  // Close modal when user successfully authenticates
  useEffect(() => {
    if (session) {
      onClose();
    }
  }, [session, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/20 backdrop-blur-sm transition-all duration-300"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white/95 backdrop-blur-md rounded-3xl shadow-2xl max-w-md w-full p-8 transform transition-all duration-300 border border-white/20">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-6 right-6 text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100/50"
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Header */}
          <div className="mb-8 text-center">
            <h2 className="text-2xl font-light text-gray-800 mb-3">
              {mode === 'sign_up' ? 'Hey there! Let\'s get you set up 🌟' : 'Welcome back! 👋'}
            </h2>
            <p className="text-gray-500 font-light leading-relaxed">
              {mode === 'sign_up'
                ? 'Get 10 free voice analyses, then just $20/month for unlimited access'
                : 'Ready to dive back into your voice insights?'
              }
            </p>
          </div>

          {/* Auth Form */}
          <Auth
            supabaseClient={supabase}
            view={mode}
            appearance={{
              theme: ThemeSupa,
              variables: {
                default: {
                  colors: {
                    brand: '#2563eb',
                    brandAccent: '#1d4ed8',
                  },
                },
              },
              className: {
                container: 'w-full',
                button: 'w-full px-6 py-3 border border-transparent text-sm font-medium rounded-2xl text-white bg-gradient-to-r from-orange-400 to-amber-500 hover:from-orange-500 hover:to-amber-600 focus:outline-none focus:ring-2 focus:ring-orange-300/30 transition-all duration-200 shadow-sm hover:shadow-md',
                input: 'appearance-none rounded-2xl relative block w-full px-4 py-3 border border-orange-200/50 placeholder-gray-400 text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-300/30 focus:border-orange-300 transition-all bg-white/50',
                label: 'block text-sm font-light text-gray-600 mb-2',
                message: 'text-sm text-red-500 mt-1 font-light',
              },
            }}
            providers={['google']}
            redirectTo={`${window.location.origin}/auth/callback`}
            showLinks={false}
            localization={{
              variables: {
                sign_in: {
                  email_label: 'Your email',
                  password_label: 'Password',
                  button_label: 'Let me in!',
                  loading_button_label: 'Getting you in...',
                  social_provider_text: 'Continue with {{provider}}',
                },
                sign_up: {
                  email_label: 'Your email',
                  password_label: 'Choose a password',
                  button_label: 'Get my 10 free tries!',
                  loading_button_label: 'Setting you up...',
                  social_provider_text: 'Continue with {{provider}}',
                },
              },
            }}
          />

          {/* Mode Toggle */}
          <div className="mt-8 text-center">
            <button
              onClick={() => setMode(mode === 'sign_in' ? 'sign_up' : 'sign_in')}
              className="text-sm text-gray-500 hover:text-gray-700 transition-colors font-light"
            >
              {mode === 'sign_in'
                ? "New here? Let's get you started!"
                : "Already have an account? Welcome back!"
              }
            </button>
          </div>

          {/* Alpha Program Notice */}
          {mode === 'sign_up' && (
            <div className="mt-6 p-4 bg-gradient-to-r from-orange-50/50 to-amber-50/50 rounded-2xl border border-orange-100/50">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <span className="text-orange-400 text-lg">🎉</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-orange-700 font-light leading-relaxed">
                    <span className="font-medium">Sweet deal:</span> 10 free tries, then just $20/month for unlimited access. Alpha pricing means you're getting in early!
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
